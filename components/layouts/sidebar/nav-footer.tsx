'use client';
import { Cog, LogOut, Moon, Sun } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import { toast } from 'sonner';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuItem } from '@/components/ui/sidebar';
import { clearAllStores } from '@/lib/store/clear-stores';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { cn } from '@/lib/utils';

export function NavFooter() {
  const { setTheme } = useTheme();
  const router = useRouter();

  function signOut() {
    return new Promise((resolve, reject) => {
      supabaseClient.auth.signOut().then(({ error }) => {
        if (error) {
          reject(error);
          return;
        }
        router.push('/');
        clearAllStores();
        resolve(true);
      });
    });
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <div className='flex items-center justify-between'>
          <Button
            className=''
            onClick={() =>
              toast.promise(signOut, {
                loading: 'Logging Out',
                success: 'Logged out successfully',
                error: (error) => `Failed to log out: ${error.message}`,
              })
            }
            variant={'outline'}
            size={'icon_s'}
          >
            <LogOut className='size-4' />
          </Button>

          <div className='flex items-center space-x-3'>
            <div>
              <Link
                href={'#'}
                className={cn(
                  '',
                  buttonVariants({ variant: 'outline', size: 'icon_s' })
                )}
              >
                <Cog className='size-4' />
              </Link>
            </div>
            <div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='outline' size='icon_s'>
                    <Sun className='h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90' />
                    <Moon className='absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0' />
                    <span className='sr-only'>Toggle theme</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuItem onClick={() => setTheme('light')}>
                    Light
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setTheme('dark')}>
                    Dark
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setTheme('system')}>
                    System
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
