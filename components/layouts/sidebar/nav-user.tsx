'use client';

import { ChevronsUpDown, Search } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import type { User } from '@/lib/supabase/database-modules';
import { CreateNewIssue } from './create-new-issue';

export function NavUser({ user }: { user: User }) {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <div className='flex items-center justify-between'>
          <div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size='lg'
                  className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'
                >
                  <Avatar className='size-5 '>
                    <AvatarImage src={user.avatar} alt={user.name ?? 'User'} />
                    <AvatarFallback className=''>
                      {user.name
                        ? user.name
                            .split(' ')
                            .map((word) => word.charAt(0))
                            .join('')
                        : 'UN'}
                    </AvatarFallback>
                  </Avatar>
                  <div className='grid flex-1 text-left text-sm leading-tight'>
                    <span className='truncate font-medium'>{user.role}</span>
                  </div>
                  <ChevronsUpDown className='ml-auto size-4' />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='start' side='bottom'>
                <DropdownMenuLabel className='p-0 font-normal'>
                  <div className='flex items-center gap-2 px-1 py-1.5 text-left text-sm'>
                    <Avatar className='size-8 '>
                      <AvatarImage
                        src={user.avatar}
                        alt={user.name ?? 'User'}
                      />
                      <AvatarFallback className=''>
                        {' '}
                        {user.name
                          ? user.name
                              .split(' ')
                              .map((word) => word.charAt(0))
                              .join('')
                          : 'UN'}
                      </AvatarFallback>
                    </Avatar>
                    <div className='grid flex-1 text-left text-sm leading-tight'>
                      <span className='truncate font-medium'>{user.name}</span>
                      <span className='truncate text-xs text-neutral-800 dark:text-neutral-500'>
                        {user.email}
                      </span>
                    </div>
                  </div>
                </DropdownMenuLabel>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className='flex items-center space-x-3'>
            <div>
              <Button
                className='size-8 shrink-0'
                onClick={() => console.log('search')}
                variant={'outline'}
                size={'icon_s'}
              >
                <Search className='size-4' />
              </Button>
            </div>
            <CreateNewIssue />
          </div>
        </div>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
