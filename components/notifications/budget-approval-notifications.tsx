'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bell,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Eye,
  X,
  XCircle,
} from 'lucide-react';
import { useMemo, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  useBudgets,
  useMembers,
  useProfile,
  useProjects,
} from '@/hooks/use-db';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/currency';

interface BudgetNotification {
  id: string;
  type: 'approval_required' | 'approved' | 'rejected' | 'overdue';
  budgetId: string;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionRequired: boolean;
}

interface BudgetApprovalNotificationsProps {
  className?: string;
}

export function BudgetApprovalNotifications({
  className,
}: BudgetApprovalNotificationsProps) {
  const { budgets } = useBudgets();
  const { projects } = useProjects();
  const { members } = useMembers();
  const { profile } = useProfile();

  const [isNotificationDialogOpen, setIsNotificationDialogOpen] =
    useState(false);
  const [selectedNotification, setSelectedNotification] =
    useState<BudgetNotification | null>(null);

  // Generate notifications based on budget status and user role
  const notifications = useMemo((): BudgetNotification[] => {
    if (!profile) return [];

    const userRole = profile.role;
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const notificationList: BudgetNotification[] = [];

    budgets.forEach((budget) => {
      const project = projects.find((p) => p.id === budget.ProjectId);
      const budgetAge = new Date(budget.created_at);

      // Approval required notifications
      if (budget.Status === 'draft') {
        const canApprove =
          (budget.ActualAmount < 1000 && userRole === 'Admin') ||
          (budget.ActualAmount >= 1000 &&
            budget.ActualAmount < 5000 &&
            userRole === 'Admin') ||
          (budget.ActualAmount >= 5000 && userRole === 'Admin');

        if (canApprove) {
          const isOverdue = budgetAge < sevenDaysAgo;

          notificationList.push({
            id: `approval-${budget.id}`,
            type: isOverdue ? 'overdue' : 'approval_required',
            budgetId: budget.id,
            title: isOverdue
              ? 'Overdue Budget Approval'
              : 'Budget Approval Required',
            message: `${project?.name || 'Unknown Project'} - ${formatCurrency(budget.ActualAmount, budget.Currency)} requires your approval`,
            timestamp: budget.created_at,
            read: false,
            priority: isOverdue
              ? 'high'
              : budget.ActualAmount >= 5000
                ? 'high'
                : 'medium',
            actionRequired: true,
          });
        }
      }

      // Recently approved notifications
      if (budget.Status === 'approved' && budget.ApprovalDate) {
        const approvalDate = new Date(budget.ApprovalDate);
        const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);

        if (approvalDate > threeDaysAgo) {
          const approver = members.find((m) => m.id === budget.ApprovedBy);

          notificationList.push({
            id: `approved-${budget.id}`,
            type: 'approved',
            budgetId: budget.id,
            title: 'Budget Approved',
            message: `${project?.name || 'Unknown Project'} budget approved by ${approver?.full_name || 'Unknown'}`,
            timestamp: budget.ApprovalDate,
            read: false,
            priority: 'low',
            actionRequired: false,
          });
        }
      }
    });

    // Sort by priority and timestamp
    return notificationList.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff =
        priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;

      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
  }, [budgets, projects, members, profile]);

  const unreadCount = notifications.filter((n) => !n.read).length;
  const actionRequiredCount = notifications.filter(
    (n) => n.actionRequired && !n.read
  ).length;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'approval_required':
        return <Clock className='h-4 w-4 text-yellow-600' />;
      case 'approved':
        return <CheckCircle className='h-4 w-4 text-green-600' />;
      case 'rejected':
        return <XCircle className='h-4 w-4 text-red-600' />;
      case 'overdue':
        return <AlertTriangle className='h-4 w-4 text-red-600' />;
      default:
        return <Bell className='h-4 w-4 text-gray-600' />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const markAsRead = (notificationId: string) => {
    // In real implementation, this would update the notification status
    console.log('Marking notification as read:', notificationId);
  };

  const handleNotificationClick = (notification: BudgetNotification) => {
    setSelectedNotification(notification);
    markAsRead(notification.id);
  };

  return (
    <div className={className}>
      <Dialog
        open={isNotificationDialogOpen}
        onOpenChange={setIsNotificationDialogOpen}
      >
        <DialogTrigger asChild>
          <Button variant='outline' size='sm' className='relative'>
            <Bell className='h-4 w-4' />
            {unreadCount > 0 && (
              <Badge className='absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs'>
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className='max-w-2xl max-h-[80vh]'>
          <DialogHeader>
            <DialogTitle className='flex items-center gap-2'>
              <Bell className='h-5 w-5' />
              Budget Approval Notifications
            </DialogTitle>
            <DialogDescription>
              {unreadCount > 0 ? (
                <>
                  {unreadCount} unread notifications
                  {actionRequiredCount > 0 &&
                    ` • ${actionRequiredCount} require action`}
                </>
              ) : (
                'All notifications read'
              )}
            </DialogDescription>
          </DialogHeader>

          <ScrollArea className='max-h-[60vh]'>
            <div className='space-y-3'>
              {notifications.length > 0 ? (
                notifications.map((notification) => (
                  <Card
                    key={notification.id}
                    className={cn(
                      'cursor-pointer transition-colors hover:bg-muted/50',
                      !notification.read &&
                        'border-l-4 border-l-blue-500 bg-blue-50/50'
                    )}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <CardContent className='p-4'>
                      <div className='flex items-start gap-3'>
                        <div className='mt-1'>
                          {getNotificationIcon(notification.type)}
                        </div>

                        <div className='flex-1 min-w-0'>
                          <div className='flex items-center gap-2 mb-1'>
                            <h4 className='font-medium text-sm'>
                              {notification.title}
                            </h4>
                            <Badge
                              className={getPriorityColor(
                                notification.priority
                              )}
                            >
                              {notification.priority}
                            </Badge>
                            {notification.actionRequired && (
                              <Badge variant='outline' className='text-xs'>
                                Action Required
                              </Badge>
                            )}
                          </div>

                          <p className='text-sm text-muted-foreground mb-2'>
                            {notification.message}
                          </p>

                          <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                            <div className='flex items-center gap-1'>
                              <Calendar className='h-3 w-3' />
                              {formatDate(notification.timestamp)}
                            </div>
                            <div className='flex items-center gap-1'>
                              <DollarSign className='h-3 w-3' />
                              Budget ID: {notification.budgetId.slice(-8)}
                            </div>
                          </div>
                        </div>

                        <div className='flex items-center gap-1'>
                          {notification.actionRequired && (
                            <Button
                              size='sm'
                              variant='outline'
                              className='h-8 px-2 text-xs'
                            >
                              <Eye className='h-3 w-3 mr-1' />
                              Review
                            </Button>
                          )}
                          <Button
                            size='sm'
                            variant='ghost'
                            className='h-8 w-8 p-0'
                            onClick={(e) => {
                              e.stopPropagation();
                              markAsRead(notification.id);
                            }}
                          >
                            <X className='h-3 w-3' />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className='text-center py-8 text-muted-foreground'>
                  <Bell className='h-12 w-12 mx-auto mb-4 opacity-50' />
                  <p>No notifications</p>
                  <p className='text-sm'>You're all caught up!</p>
                </div>
              )}
            </div>
          </ScrollArea>

          {notifications.length > 0 && (
            <div className='flex justify-between items-center pt-4 border-t'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => {
                  notifications.forEach((n) => markAsRead(n.id));
                }}
              >
                Mark All as Read
              </Button>
              <div className='text-sm text-muted-foreground'>
                {notifications.length} total notifications
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Notification Detail Dialog */}
      {selectedNotification && (
        <Dialog
          open={!!selectedNotification}
          onOpenChange={() => setSelectedNotification(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle className='flex items-center gap-2'>
                {getNotificationIcon(selectedNotification.type)}
                {selectedNotification.title}
              </DialogTitle>
              <DialogDescription>Budget notification details</DialogDescription>
            </DialogHeader>

            <div className='space-y-4'>
              <div className='p-4 bg-muted rounded-lg'>
                <div className='text-sm font-medium mb-2'>
                  Notification Details
                </div>
                <div className='space-y-2 text-sm'>
                  <div>Type: {selectedNotification.type.replace('_', ' ')}</div>
                  <div>Priority: {selectedNotification.priority}</div>
                  <div>Budget ID: {selectedNotification.budgetId}</div>
                  <div>Time: {formatDate(selectedNotification.timestamp)}</div>
                </div>
              </div>

              <div>
                <div className='text-sm font-medium mb-2'>Message</div>
                <p className='text-sm text-muted-foreground'>
                  {selectedNotification.message}
                </p>
              </div>

              {selectedNotification.actionRequired && (
                <div className='flex gap-3'>
                  <Button className='flex-1'>Review Budget</Button>
                  <Button variant='outline'>Dismiss</Button>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
