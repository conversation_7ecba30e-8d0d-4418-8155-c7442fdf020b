'use client';

import { Building2 } from 'lucide-react';
import { useMemo } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useClients } from '@/hooks/use-db';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
  loading?: boolean;
}

function MetricCard({
  title,
  value,
  subtitle,
  variant = 'default',
  icon,
  loading = false,
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'destructive':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default:
        return 'border-border bg-card';
    }
  };

  if (loading) {
    return (
      <Card className={getVariantStyles()}>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            <Skeleton className='h-4 w-24' />
          </CardTitle>
          <Skeleton className='h-4 w-4' />
        </CardHeader>
        <CardContent>
          <Skeleton className='h-8 w-16 mb-1' />
          <Skeleton className='h-3 w-32' />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={getVariantStyles()}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium'>{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className='text-2xl font-bold'>{value}</div>
        <p className='text-xs text-muted-foreground'>{subtitle}</p>
      </CardContent>
    </Card>
  );
}

export function DatabaseMetrics() {
  const { clients, loading: clientsLoading } = useClients();

  const metrics = useMemo(() => {
    // Calculate total budget amount

    // Calculate recent activity (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentClients = clients.filter(
      (c) => new Date(c.created_at) > sevenDaysAgo
    ).length;

    return [
      {
        title: 'Total Clients',
        value: clients.length,
        subtitle: `${recentClients} added this week`,
        icon: <Building2 className='h-4 w-4 text-blue-600' />,
        variant: 'default' as const,
        loading: clientsLoading,
      },
    ];
  }, [clients, clientsLoading]);

  return (
    <div className='space-y-4'>
      <div>
        <h2 className='text-lg font-semibold'>Database Overview</h2>
        <p className='text-sm text-muted-foreground'>
          Real-time statistics and health metrics for all database tables
        </p>
      </div>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {metrics.map((metric, index) => (
          <MetricCard
            key={`${metric.title}+${index}`}
            title={metric.title}
            value={metric.value}
            subtitle={metric.subtitle}
            variant={metric.variant}
            icon={metric.icon}
            loading={metric.loading}
          />
        ))}
      </div>
    </div>
  );
}
