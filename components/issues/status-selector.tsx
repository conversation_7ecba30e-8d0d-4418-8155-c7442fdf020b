'use client';

import { CheckIcon } from 'lucide-react';
import { useEffect, useId, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useIssues, useStatus } from '@/hooks/use-db';
import { StatusIcon } from '@/lib/constants/status';

interface StatusOption {
  id: string;
  name: string;
  color: string;
  sort_order: number;
}

interface StatusSelectorProps {
  status: StatusOption | null;
  issueId?: string;
  onChange?: (status: StatusOption | null) => void;
}

export function StatusSelector({
  status,
  issueId,
  onChange,
}: StatusSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>(status?.id || '');
  const { updateIssue } = useIssues();
  const { status: statusOptions, loading } = useStatus();

  useEffect(() => {
    setValue(status?.id || '');
  }, [status?.id]);

  // Status options are now loaded via useStatus hook

  const handleStatusChange = (statusId: string) => {
    setValue(statusId);
    setOpen(false);

    const foundStatus = statusOptions.find((s) => s.id === statusId) || null;
    const newStatus: StatusOption | null = foundStatus
      ? {
          ...foundStatus,
          sort_order: foundStatus.sort_order ?? 0, // default to 0 if null
        }
      : null;

    if (onChange) {
      onChange(newStatus);
    }

    if (issueId && newStatus) {
      updateIssue(issueId, { status_id: newStatus.id })
        .then(() => {
          console.log('Issue status updated successfully');
        })
        .catch((error) => {
          console.error('Failed to update issue status:', error);
        });
    }
  };

  return (
    <div className='*:not-first:mt-2'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='size-7 flex items-center justify-center'
            size='icon_s'
            variant='ghost'
            // role='combobox'
            aria-expanded={open}
          >
            {(() => {
              const selectedItem = statusOptions.find(
                (item) => item.id === value
              );
              if (selectedItem) {
                return <StatusIcon statusId={selectedItem.id} />;
              }
              return null;
            })()}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
          align='start'
        >
          <Command>
            <CommandInput placeholder='Set status...' />
            <CommandList>
              <CommandEmpty>No status found.</CommandEmpty>
              <CommandGroup>
                {loading ? (
                  <CommandItem disabled>
                    <span>Loading statuses...</span>
                  </CommandItem>
                ) : (
                  statusOptions.map((item) => (
                    <CommandItem
                      key={item.id}
                      value={item.id}
                      onSelect={handleStatusChange}
                      className='flex items-center justify-between'
                    >
                      <div className='flex items-center gap-2'>
                        <StatusIcon statusId={item.id} />
                        {item.name}
                      </div>
                      {value === item.id && (
                        <CheckIcon size={16} className='ml-auto' />
                      )}
                    </CommandItem>
                  ))
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
