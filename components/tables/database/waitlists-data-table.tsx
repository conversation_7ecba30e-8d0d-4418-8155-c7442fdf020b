'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table';
import { useWaitlists } from '@/hooks/use-db';
import type { Waitlist } from '@/lib/supabase/database-modules';

import { createColumns } from './waitlists-columns';

export function WaitlistsDataTable() {
  const { waitlists, loading, error, updateWaitlist, deleteWaitlist } =
    useWaitlists();
  const router = useRouter();
  const [selectedWaitlist, setSelectedWaitlist] = useState<Waitlist | null>(
    null
  );

  const handleViewWaitlist = (waitlist: Waitlist) => {
    // Navigate to waitlist detail page if it exists
    router.push(`/admin/database/waitlists?id=${waitlist.id}`);
  };

  const handleEditWaitlist = (waitlist: Waitlist) => {
    setSelectedWaitlist(waitlist);
    // TODO: Open edit dialog/modal
  };

  const handleDeleteWaitlist = (waitlist: Waitlist) => {
    const waitlistName = waitlist.full_name || 'Unknown Person';
    if (
      confirm(
        `Are you sure you want to delete the waitlist entry for ${waitlistName}?`
      )
    ) {
      const deletePromise = deleteWaitlist(waitlist.id);

      deletePromise
        .then(() => {
          toast.success(
            `Waitlist entry for ${waitlistName} has been deleted successfully`
          );
        })
        .catch((error) => {
          toast.error(
            `Failed to delete waitlist entry: ${error?.message || 'Unknown error'}`
          );
        });
    }
  };

  const handleEmailStatusChange = (
    waitlistId: number,
    isEmailSent: boolean
  ) => {
    const updatePromise = updateWaitlist(waitlistId, {
      is_ld_email_sent: isEmailSent,
    });

    updatePromise
      .then(() => {
        toast.success(
          `Email status updated to ${isEmailSent ? 'sent' : 'pending'}`
        );
      })
      .catch((error) => {
        toast.error(
          `Failed to update email status: ${error?.message || 'Unknown error'}`
        );
      });
  };

  const columns = createColumns({
    onViewWaitlist: handleViewWaitlist,
    onEditWaitlist: handleEditWaitlist,
    onDeleteWaitlist: handleDeleteWaitlist,
    onEmailStatusChange: handleEmailStatusChange,
  });

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>Error: {error}</div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div>
        <h2 className='text-lg font-semibold'>Waitlists Database</h2>
        <p className='text-sm text-muted-foreground'>
          Manage all waitlist entries with full CRUD operations
        </p>
      </div>
      <DataTable columns={columns} data={waitlists} isLoading={loading} />
    </div>
  );
}
