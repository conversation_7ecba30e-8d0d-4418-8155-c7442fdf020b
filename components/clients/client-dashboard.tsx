'use client';

import {
  Archive,
  Building,
  CheckCircle,
  Clock,
  DollarSign,
  Mail,
  TrendingUp,
} from 'lucide-react';
import { useMemo } from 'react';

import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  useBudgets,
  useClients,
  useProjects,
  useProposals,
} from '@/hooks/use-db';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/format-currency';

interface ClientDashboardProps {
  className?: string;
}

export function ClientDashboard({ className }: ClientDashboardProps) {
  const { clients } = useClients();
  const { projects } = useProjects();
  const { budgets } = useBudgets();
  const { proposals } = useProposals();

  const analytics = useMemo(() => {
    const activeClients = clients.filter((c) => c.is_active);
    const archivedClients = clients.filter((c) => !c.is_active);

    // Industry distribution
    const industryStats = clients.reduce(
      (acc, client) => {
        const industry = client.industry || 'Other';
        acc[industry] = (acc[industry] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    // Client value analysis
    const clientValues = clients.map((client) => {
      const clientBudgets = budgets.filter((b) => b.ClientId === client.id);
      const totalValue = clientBudgets.reduce(
        (sum, b) => sum + b.ActualAmount,
        0
      );
      const clientProjects = projects.filter((p) => p.client_id === client.id);
      const clientProposals = proposals.filter(
        (p) => p.client_id === client.id
      );

      return {
        client,
        totalValue,
        projectCount: clientProjects.length,
        proposalCount: clientProposals.length,
        activeProjects: clientProjects.filter((p) => !p.is_archived).length,
      };
    });

    const totalClientValue = clientValues.reduce(
      (sum, cv) => sum + cv.totalValue,
      0
    );
    const averageClientValue = totalClientValue / (clients.length || 1);

    // Top clients by value
    const topClients = clientValues
      .sort((a, b) => b.totalValue - a.totalValue)
      .slice(0, 5);

    // Recent clients
    const recentClients = clients
      .sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
      .slice(0, 5);

    // Clients without projects
    const clientsWithoutProjects = clients.filter(
      (client) => !projects.some((p) => p.client_id === client.id)
    );

    // Clients with contact info
    const clientsWithEmail = clients.filter((c) => c.email).length;
    const clientsWithPhone = clients.filter((c) => c.phone).length;
    const clientsWithCompany = clients.filter((c) => c.company_name).length;

    return {
      totalClients: clients.length,
      activeClients: activeClients.length,
      archivedClients: archivedClients.length,
      industryStats,
      totalClientValue,
      averageClientValue,
      topClients,
      recentClients,
      clientsWithoutProjects,
      contactStats: {
        withEmail: clientsWithEmail,
        withPhone: clientsWithPhone,
        withCompany: clientsWithCompany,
      },
    };
  }, [clients, projects, budgets, proposals]);

  const getIndustryColor = (industry: string) => {
    const colors: Record<string, string> = {
      Technology: 'bg-blue-100 text-blue-800',
      Healthcare: 'bg-green-100 text-green-800',
      Finance: 'bg-yellow-100 text-yellow-800',
      Education: 'bg-purple-100 text-purple-800',
      Retail: 'bg-pink-100 text-pink-800',
      Manufacturing: 'bg-orange-100 text-orange-800',
      'Real Estate': 'bg-indigo-100 text-indigo-800',
      'Marketing & Advertising': 'bg-red-100 text-red-800',
      Consulting: 'bg-teal-100 text-teal-800',
      Other: 'bg-gray-100 text-gray-800',
    };
    return colors[industry] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Overview Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Clients</CardTitle>
            <Building className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{analytics.totalClients}</div>
            <p className='text-xs text-muted-foreground'>
              {analytics.activeClients} active, {analytics.archivedClients}{' '}
              archived
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Client Value</CardTitle>
            <DollarSign className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(analytics.totalClientValue, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              Avg: {formatCurrency(analytics.averageClientValue, 'USD')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Contact Coverage
            </CardTitle>
            <Mail className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {Math.round(
                (analytics.contactStats.withEmail / analytics.totalClients) *
                  100
              )}
              %
            </div>
            <p className='text-xs text-muted-foreground'>
              {analytics.contactStats.withEmail} have email addresses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Engagement</CardTitle>
            <TrendingUp className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {analytics.totalClients - analytics.clientsWithoutProjects.length}
            </div>
            <p className='text-xs text-muted-foreground'>
              Clients with active projects
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Industry Distribution & Top Clients */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        <Card>
          <CardHeader>
            <CardTitle>Industry Distribution</CardTitle>
            <CardDescription>
              Client distribution across industries
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            {Object.entries(analytics.industryStats)
              .sort(([, a], [, b]) => b - a)
              .map(([industry, count]) => {
                const percentage = (count / analytics.totalClients) * 100;
                return (
                  <div key={industry} className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium'>{industry}</span>
                      <div className='flex items-center gap-2'>
                        <span className='text-sm text-muted-foreground'>
                          {count} clients
                        </span>
                        <Badge className={getIndustryColor(industry)}>
                          {percentage.toFixed(1)}%
                        </Badge>
                      </div>
                    </div>
                    <Progress value={percentage} className='h-2' />
                  </div>
                );
              })}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Clients by Value</CardTitle>
            <CardDescription>
              Highest value clients by total budget
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {analytics.topClients.map((clientData, index) => (
                <div
                  key={clientData.client.id}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center gap-3'>
                    <div className='w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm font-medium'>
                      {index + 1}
                    </div>
                    <div>
                      <div className='font-medium'>
                        {clientData.client.name}
                      </div>
                      <div className='text-sm text-muted-foreground'>
                        {clientData.projectCount} projects •{' '}
                        {clientData.proposalCount} proposals
                      </div>
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='font-medium'>
                      {formatCurrency(clientData.totalValue, 'USD')}
                    </div>
                    <div className='text-sm text-muted-foreground'>
                      {clientData.activeProjects} active
                    </div>
                  </div>
                </div>
              ))}
              {analytics.topClients.length === 0 && (
                <div className='text-center py-4 text-muted-foreground'>
                  No client data available
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity & Alerts */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        <Card>
          <CardHeader>
            <CardTitle>Recent Clients</CardTitle>
            <CardDescription>Latest client additions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {analytics.recentClients.map((client) => (
                <div
                  key={client.id}
                  className='flex items-center justify-between'
                >
                  <div>
                    <div className='font-medium'>{client.name}</div>
                    <div className='text-sm text-muted-foreground'>
                      {client.company_name || client.industry || 'No company'}
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='text-sm'>
                      {formatDate(client.created_at)}
                    </div>
                    <Badge
                      className={
                        client.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }
                    >
                      {client.is_active ? 'Active' : 'Archived'}
                    </Badge>
                  </div>
                </div>
              ))}
              {analytics.recentClients.length === 0 && (
                <div className='text-center py-4 text-muted-foreground'>
                  No recent clients
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>Client contact data completeness</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Email Addresses</span>
                <span>
                  {analytics.contactStats.withEmail}/{analytics.totalClients}
                </span>
              </div>
              <Progress
                value={
                  (analytics.contactStats.withEmail / analytics.totalClients) *
                  100
                }
                className='h-2'
              />
            </div>

            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Phone Numbers</span>
                <span>
                  {analytics.contactStats.withPhone}/{analytics.totalClients}
                </span>
              </div>
              <Progress
                value={
                  (analytics.contactStats.withPhone / analytics.totalClients) *
                  100
                }
                className='h-2'
              />
            </div>

            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Company Names</span>
                <span>
                  {analytics.contactStats.withCompany}/{analytics.totalClients}
                </span>
              </div>
              <Progress
                value={
                  (analytics.contactStats.withCompany /
                    analytics.totalClients) *
                  100
                }
                className='h-2'
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Client Alerts</CardTitle>
            <CardDescription>Clients requiring attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {analytics.clientsWithoutProjects.length > 0 && (
                <div className='p-3 bg-yellow-50 border border-yellow-200 rounded-lg'>
                  <div className='flex items-center gap-2 text-yellow-800'>
                    <Clock className='h-4 w-4' />
                    <span className='text-sm font-medium'>No Projects</span>
                  </div>
                  <div className='text-xs text-yellow-700 mt-1'>
                    {analytics.clientsWithoutProjects.length} clients without
                    projects
                  </div>
                </div>
              )}

              {analytics.contactStats.withEmail <
                analytics.totalClients * 0.8 && (
                <div className='p-3 bg-blue-50 border border-blue-200 rounded-lg'>
                  <div className='flex items-center gap-2 text-blue-800'>
                    <Mail className='h-4 w-4' />
                    <span className='text-sm font-medium'>
                      Missing Contacts
                    </span>
                  </div>
                  <div className='text-xs text-blue-700 mt-1'>
                    {analytics.totalClients - analytics.contactStats.withEmail}{' '}
                    clients missing email
                  </div>
                </div>
              )}

              {analytics.archivedClients > 0 && (
                <div className='p-3 bg-gray-50 border border-gray-200 rounded-lg'>
                  <div className='flex items-center gap-2 text-gray-800'>
                    <Archive className='h-4 w-4' />
                    <span className='text-sm font-medium'>
                      Archived Clients
                    </span>
                  </div>
                  <div className='text-xs text-gray-700 mt-1'>
                    {analytics.archivedClients} archived clients
                  </div>
                </div>
              )}

              {analytics.clientsWithoutProjects.length === 0 &&
                analytics.contactStats.withEmail >=
                  analytics.totalClients * 0.8 &&
                analytics.archivedClients === 0 && (
                  <div className='p-3 bg-green-50 border border-green-200 rounded-lg'>
                    <div className='flex items-center gap-2 text-green-800'>
                      <CheckCircle className='h-4 w-4' />
                      <span className='text-sm font-medium'>All Good!</span>
                    </div>
                    <div className='text-xs text-green-700 mt-1'>
                      No client issues detected
                    </div>
                  </div>
                )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
