'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  ArrowRight,
  CheckCircle,
  Clock,
  Users,
  XCircle,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useBudgets, useMembers, useProfile } from '@/hooks/use-db';
import type { Budget } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/format-currency';

interface ApprovalStep {
  id: string;
  role: 'Manager' | 'Admin' | 'Finance';
  required: boolean;
  order: number;
  status: 'pending' | 'approved' | 'rejected' | 'skipped';
  approver?: string;
  approvedAt?: string;
  comments?: string;
}

interface BudgetApprovalSystemProps {
  budget: Budget;
  className?: string;
}

export function BudgetApprovalSystem({
  budget,
  className,
}: BudgetApprovalSystemProps) {
  const { approveBudget, updateBudget } = useBudgets();
  const { members } = useMembers();
  const { profile } = useProfile();

  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false);
  const [isRejectionDialogOpen, setIsRejectionDialogOpen] = useState(false);
  const [selectedApprover, setSelectedApprover] = useState('');
  const [approvalComments, setApprovalComments] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define approval workflow based on budget amount
  const approvalWorkflow = useMemo((): ApprovalStep[] => {
    const amount = budget.ActualAmount;

    if (amount < 1000) {
      // Small budgets: Manager approval only
      return [
        {
          id: 'manager',
          role: 'Manager',
          required: true,
          order: 1,
          status: budget.Status === 'approved' ? 'approved' : 'pending',
          approver: budget.ApprovedBy || undefined,
          approvedAt: budget.ApprovalDate || undefined,
        },
      ];
    } else if (amount < 5000) {
      // Medium budgets: Manager + Admin approval
      return [
        {
          id: 'manager',
          role: 'Manager',
          required: true,
          order: 1,
          status: 'pending',
        },
        {
          id: 'admin',
          role: 'Admin',
          required: true,
          order: 2,
          status: 'pending',
        },
      ];
    } else {
      // Large budgets: Manager + Admin + Finance approval
      return [
        {
          id: 'manager',
          role: 'Manager',
          required: true,
          order: 1,
          status: 'pending',
        },
        {
          id: 'admin',
          role: 'Admin',
          required: true,
          order: 2,
          status: 'pending',
        },
        {
          id: 'finance',
          role: 'Finance',
          required: true,
          order: 3,
          status: 'pending',
        },
      ];
    }
  }, [
    budget.ActualAmount,
    budget.Status,
    budget.ApprovedBy,
    budget.ApprovalDate,
  ]);

  // Get current approval step
  const currentStep = useMemo(() => {
    return (
      approvalWorkflow.find((step) => step.status === 'pending') ||
      approvalWorkflow[0]
    );
  }, [approvalWorkflow]);

  // Check if user can approve current step
  const canApprove = useMemo(() => {
    if (!profile || budget.Status !== 'draft') return false;

    const userRole = profile.role;
    const currentStepRole = currentStep?.role;

    // Check if user has the required role for current step
    return userRole === currentStepRole || userRole === 'Admin';
  }, [profile, budget.Status, currentStep]);

  // Get members by role
  const membersByRole = useMemo(() => {
    const roleMap: Record<string, typeof members> = {
      Manager: members.filter((m) => m.role === 'Admin'), // Use Admin for Manager role
      Admin: members.filter((m) => m.role === 'Admin'),
      Finance: members.filter((m) => m.role === 'Admin'), // Use Admin for Finance role
    };
    return roleMap;
  }, [members]);

  const getStepIcon = (step: ApprovalStep) => {
    switch (step.status) {
      case 'approved':
        return <CheckCircle className='h-5 w-5 text-green-600' />;
      case 'rejected':
        return <XCircle className='h-5 w-5 text-red-600' />;
      case 'pending':
        return <Clock className='h-5 w-5 text-yellow-600' />;
      default:
        return <Clock className='h-5 w-5 text-gray-400' />;
    }
  };

  const getStepColor = (step: ApprovalStep) => {
    switch (step.status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleApproval = async () => {
    if (!selectedApprover) {
      toast.error('Please select an approver');
      return;
    }

    setIsSubmitting(true);
    try {
      // In a real implementation, this would update the approval workflow
      await approveBudget(budget.id, selectedApprover);

      // Send notification (mock)
      await sendNotification({
        type: 'budget_approved',
        budgetId: budget.id,
        approver: selectedApprover,
        comments: approvalComments,
      });

      toast.success('Budget approved successfully');
      setIsApprovalDialogOpen(false);
      setSelectedApprover('');
      setApprovalComments('');
    } catch (error) {
      console.error('Error approving budget:', error);
      toast.error(
        `Failed to approve budget: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRejection = async () => {
    if (!rejectionReason.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    setIsSubmitting(true);
    try {
      // In a real implementation, this would update the budget status and workflow
      await updateBudget(budget.id, {
        Status: 'draft', // Keep as draft for revision rather than rejected
      });

      // Send notification (mock)
      await sendNotification({
        type: 'budget_rejected',
        budgetId: budget.id,
        reason: rejectionReason,
      });

      toast.success('Budget rejected');
      setIsRejectionDialogOpen(false);
      setRejectionReason('');
    } catch (error) {
      console.error('Error rejecting budget:', error);
      toast.error(
        `Failed to reject budget: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Mock notification function
  const sendNotification = async (notification: Record<string, unknown>) => {
    // In real implementation, this would send email/push notifications
    console.log('Sending notification:', notification);
    await new Promise((resolve) => setTimeout(resolve, 500));
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Card className={cn('', className)}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='flex items-center gap-2'>
              <Users className='h-5 w-5' />
              Budget Approval Workflow
            </CardTitle>
            <CardDescription>
              Multi-step approval process based on budget amount
            </CardDescription>
          </div>
          <div className='flex gap-2'>
            {canApprove && budget.Status === 'draft' && (
              <>
                <Dialog
                  open={isApprovalDialogOpen}
                  onOpenChange={setIsApprovalDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm' className='flex items-center gap-2'>
                      <CheckCircle className='h-4 w-4' />
                      Approve
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Approve Budget</DialogTitle>
                      <DialogDescription>
                        Approve this budget for the current approval step
                      </DialogDescription>
                    </DialogHeader>
                    <div className='space-y-4'>
                      <div className='p-4 bg-muted rounded-lg'>
                        <div className='text-sm font-medium mb-2'>
                          Budget Details
                        </div>
                        <div className='grid grid-cols-2 gap-2 text-sm'>
                          <div>
                            Amount:{' '}
                            {formatCurrency(
                              budget.ActualAmount,
                              budget.Currency
                            )}
                          </div>
                          <div>Category: {budget.Category}</div>
                          <div>Current Step: {currentStep?.role}</div>
                          <div>
                            Approval Level: {approvalWorkflow.length} steps
                          </div>
                        </div>
                      </div>

                      <div>
                        <Label>Approver *</Label>
                        <Select
                          value={selectedApprover}
                          onValueChange={setSelectedApprover}
                        >
                          <SelectTrigger className='mt-2'>
                            <SelectValue placeholder='Select approver' />
                          </SelectTrigger>
                          <SelectContent>
                            {membersByRole[currentStep?.role || 'Admin']?.map(
                              (member) => (
                                <SelectItem key={member.id} value={member.id}>
                                  {member.full_name}
                                </SelectItem>
                              )
                            )}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Approval Comments</Label>
                        <Textarea
                          value={approvalComments}
                          onChange={(e) => setApprovalComments(e.target.value)}
                          placeholder='Optional comments about this approval...'
                          className='mt-2'
                          rows={3}
                        />
                      </div>

                      <div className='flex gap-3 pt-4'>
                        <Button
                          onClick={handleApproval}
                          disabled={isSubmitting || !selectedApprover}
                          className='flex-1'
                        >
                          {isSubmitting ? 'Approving...' : 'Approve Budget'}
                        </Button>
                        <Button
                          variant='outline'
                          onClick={() => setIsApprovalDialogOpen(false)}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                <Dialog
                  open={isRejectionDialogOpen}
                  onOpenChange={setIsRejectionDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      size='sm'
                      variant='destructive'
                      className='flex items-center gap-2'
                    >
                      <XCircle className='h-4 w-4' />
                      Reject
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Reject Budget</DialogTitle>
                      <DialogDescription>
                        Reject this budget and provide a reason for rejection
                      </DialogDescription>
                    </DialogHeader>
                    <div className='space-y-4'>
                      <div className='p-4 bg-red-50 border border-red-200 rounded-lg'>
                        <div className='text-sm font-medium mb-2 text-red-800'>
                          Budget to Reject
                        </div>
                        <div className='text-sm text-red-700'>
                          {formatCurrency(budget.ActualAmount, budget.Currency)}{' '}
                          • {budget.Category}
                        </div>
                      </div>

                      <div>
                        <Label>Rejection Reason *</Label>
                        <Textarea
                          value={rejectionReason}
                          onChange={(e) => setRejectionReason(e.target.value)}
                          placeholder='Please provide a detailed reason for rejecting this budget...'
                          className='mt-2'
                          rows={4}
                        />
                      </div>

                      <div className='flex gap-3 pt-4'>
                        <Button
                          variant='destructive'
                          onClick={handleRejection}
                          disabled={isSubmitting || !rejectionReason.trim()}
                          className='flex-1'
                        >
                          {isSubmitting ? 'Rejecting...' : 'Reject Budget'}
                        </Button>
                        <Button
                          variant='outline'
                          onClick={() => setIsRejectionDialogOpen(false)}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className='space-y-6'>
          {/* Approval Workflow Steps */}
          <div className='space-y-4'>
            <h3 className='font-medium'>Approval Steps</h3>
            <div className='space-y-3'>
              {approvalWorkflow.map((step, index) => (
                <div key={step.id} className='flex items-center gap-4'>
                  <div className='flex items-center gap-2'>
                    {getStepIcon(step)}
                    <div className='w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm font-medium'>
                      {index + 1}
                    </div>
                  </div>

                  <div className='flex-1'>
                    <div className='flex items-center gap-2'>
                      <span className='font-medium'>{step.role} Approval</span>
                      <Badge className={getStepColor(step)}>
                        {step.status}
                      </Badge>
                      {step.required && (
                        <Badge variant='outline' className='text-xs'>
                          Required
                        </Badge>
                      )}
                    </div>

                    {step.approver && (
                      <div className='text-sm text-muted-foreground mt-1'>
                        Approved by:{' '}
                        {members.find((m) => m.id === step.approver)?.full_name}
                        {step.approvedAt && ` • ${formatDate(step.approvedAt)}`}
                      </div>
                    )}

                    {step.comments && (
                      <div className='text-sm text-muted-foreground mt-1 italic'>
                        "{step.comments}"
                      </div>
                    )}
                  </div>

                  {index < approvalWorkflow.length - 1 && (
                    <ArrowRight className='h-4 w-4 text-muted-foreground' />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Current Status */}
          <div className='p-4 bg-muted rounded-lg'>
            <div className='flex items-center gap-2 mb-2'>
              <AlertTriangle className='h-4 w-4' />
              <span className='font-medium'>Current Status</span>
            </div>
            <div className='text-sm text-muted-foreground'>
              {budget.Status === 'draft' ? (
                <>
                  Waiting for {currentStep?.role} approval
                  {canApprove && ' • You can approve this step'}
                </>
              ) : budget.Status === 'approved' ? (
                'Budget has been fully approved and is active'
              ) : (
                `Budget status: ${budget.Status}`
              )}
            </div>
          </div>

          {/* Approval Rules */}
          <div className='space-y-2'>
            <h4 className='font-medium text-sm'>Approval Rules</h4>
            <div className='text-sm text-muted-foreground space-y-1'>
              <div>• Budgets under $1,000: Manager approval only</div>
              <div>• Budgets $1,000-$5,000: Manager + Admin approval</div>
              <div>
                • Budgets over $5,000: Manager + Admin + Finance approval
              </div>
              <div>• All approvals must be completed in sequence</div>
              <div>• Rejection at any step stops the workflow</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
