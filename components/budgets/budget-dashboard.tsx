'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bar<PERSON>hart3,
  CheckCircle,
  Clock,
  DollarSign,
  TrendingDown,
  Users,
  XCircle,
} from 'lucide-react';
import { useMemo } from 'react';

import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useBudgets } from '@/hooks/use-db';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/currency';

interface BudgetDashboardProps {
  className?: string;
}

export function BudgetDashboard({ className }: BudgetDashboardProps) {
  const { budgets, getBudgetAnalytics } = useBudgets();

  const analytics = useMemo(() => getBudgetAnalytics(), [getBudgetAnalytics]);

  const statusStats = useMemo(() => {
    const stats = {
      draft: { count: 0, color: 'bg-gray-100 text-gray-800', icon: Clock },
      approved: {
        count: 0,
        color: 'bg-green-100 text-green-800',
        icon: CheckCircle,
      },
      locked: {
        count: 0,
        color: 'bg-yellow-100 text-yellow-800',
        icon: AlertTriangle,
      },
      spent: { count: 0, color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    budgets.forEach((budget) => {
      if (stats[budget.Status as keyof typeof stats]) {
        stats[budget.Status as keyof typeof stats].count++;
      }
    });

    return stats;
  }, [budgets]);

  const categoryStats = useMemo(() => {
    const stats = {
      development: {
        amount: 0,
        count: 0,
        color: 'bg-purple-100 text-purple-800',
      },
      marketing: { amount: 0, count: 0, color: 'bg-blue-100 text-blue-800' },
      consulting: {
        amount: 0,
        count: 0,
        color: 'bg-orange-100 text-orange-800',
      },
      operations: { amount: 0, count: 0, color: 'bg-green-100 text-green-800' },
      other: { amount: 0, count: 0, color: 'bg-gray-100 text-gray-800' },
    };

    budgets.forEach((budget) => {
      if (stats[budget.Category as keyof typeof stats]) {
        stats[budget.Category as keyof typeof stats].amount +=
          budget.ActualAmount;
        stats[budget.Category as keyof typeof stats].count++;
      }
    });

    return stats;
  }, [budgets]);

  const recentBudgets = useMemo(() => {
    return budgets
      .sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
      .slice(0, 5);
  }, [budgets]);

  const overdueBudgets = useMemo(() => {
    const today = new Date();
    return budgets.filter((budget) => {
      const endDate = new Date(budget.EndDate);
      return endDate < today && budget.Status !== 'spent';
    });
  }, [budgets]);

  const lowBudgets = useMemo(() => {
    return budgets.filter((budget) => {
      const spentPercentage =
        ((budget.ActualAmount - budget.CurrentAmount) / budget.ActualAmount) *
        100;
      return spentPercentage > 80 && budget.Status === 'approved';
    });
  }, [budgets]);

  return (
    <div className={cn('space-y-6', className)}>
      {/* Overview Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Budgets</CardTitle>
            <BarChart3 className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{analytics.totalBudgets}</div>
            <p className='text-xs text-muted-foreground'>
              Active budget tracking
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Amount</CardTitle>
            <DollarSign className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(analytics.totalAmount, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>Across all budgets</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Spent</CardTitle>
            <TrendingDown className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(analytics.totalSpent, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              {analytics.spentPercentage.toFixed(1)}% of total budget
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Commissions</CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(analytics.totalCommissions, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              Affiliate commissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Budget Status Overview */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        <Card>
          <CardHeader>
            <CardTitle>Budget Status</CardTitle>
            <CardDescription>Distribution of budgets by status</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            {Object.entries(statusStats).map(([status, data]) => {
              const Icon = data.icon;
              return (
                <div key={status} className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <Icon className='h-4 w-4' />
                    <span className='capitalize'>{status}</span>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Badge className={data.color}>{data.count}</Badge>
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Budget Categories</CardTitle>
            <CardDescription>Budget allocation by category</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            {Object.entries(categoryStats)
              .sort(([, a], [, b]) => b.amount - a.amount)
              .map(([category, data]) => (
                <div key={category} className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <span className='capitalize'>{category}</span>
                    <div className='flex items-center gap-2'>
                      <span className='text-sm text-muted-foreground'>
                        {data.count} budgets
                      </span>
                      <Badge className={data.color}>
                        {formatCurrency(data.amount, 'USD')}
                      </Badge>
                    </div>
                  </div>
                  <Progress
                    value={(data.amount / analytics.totalAmount) * 100}
                    className='h-2'
                  />
                </div>
              ))}
          </CardContent>
        </Card>
      </div>

      {/* Alerts and Recent Activity */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Overdue Budgets Alert */}
        {overdueBudgets.length > 0 && (
          <Card className='border-red-200'>
            <CardHeader>
              <CardTitle className='text-red-800 flex items-center gap-2'>
                <AlertTriangle className='h-4 w-4' />
                Overdue Budgets
              </CardTitle>
              <CardDescription>
                Budgets that have passed their end date
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-2'>
                {overdueBudgets.slice(0, 3).map((budget) => (
                  <div key={budget.id} className='text-sm'>
                    <div className='font-medium'>
                      Project: {budget.ProjectId}
                    </div>
                    <div className='text-muted-foreground'>
                      Due: {new Date(budget.EndDate).toLocaleDateString()}
                    </div>
                  </div>
                ))}
                {overdueBudgets.length > 3 && (
                  <div className='text-sm text-muted-foreground'>
                    +{overdueBudgets.length - 3} more
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Low Budget Alert */}
        {lowBudgets.length > 0 && (
          <Card className='border-yellow-200'>
            <CardHeader>
              <CardTitle className='text-yellow-800 flex items-center gap-2'>
                <TrendingDown className='h-4 w-4' />
                Low Budgets
              </CardTitle>
              <CardDescription>
                Budgets with less than 20% remaining
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-2'>
                {lowBudgets.slice(0, 3).map((budget) => {
                  const spentPercentage =
                    ((budget.ActualAmount - budget.CurrentAmount) /
                      budget.ActualAmount) *
                    100;
                  return (
                    <div key={budget.id} className='text-sm'>
                      <div className='font-medium'>
                        Project: {budget.ProjectId}
                      </div>
                      <div className='text-muted-foreground'>
                        {spentPercentage.toFixed(1)}% spent
                      </div>
                    </div>
                  );
                })}
                {lowBudgets.length > 3 && (
                  <div className='text-sm text-muted-foreground'>
                    +{lowBudgets.length - 3} more
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recent Budgets */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Budgets</CardTitle>
            <CardDescription>Latest budget activity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {recentBudgets.map((budget) => (
                <div
                  key={budget.id}
                  className='flex items-center justify-between'
                >
                  <div className='space-y-1'>
                    <div className='text-sm font-medium'>
                      {formatCurrency(budget.ActualAmount, budget.Currency)}
                    </div>
                    <div className='text-xs text-muted-foreground'>
                      {budget.Category} • {budget.Status}
                    </div>
                  </div>
                  <Badge
                    className={
                      budget.Status === 'approved'
                        ? 'bg-green-100 text-green-800'
                        : budget.Status === 'draft'
                          ? 'bg-gray-100 text-gray-800'
                          : budget.Status === 'locked'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                    }
                  >
                    {budget.Status}
                  </Badge>
                </div>
              ))}
              {recentBudgets.length === 0 && (
                <div className='text-sm text-muted-foreground'>
                  No recent budget activity
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Budget Health Overview</CardTitle>
          <CardDescription>
            Overall budget performance and utilization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='text-center p-4 border rounded-lg'>
                <div className='text-2xl font-bold text-green-600'>
                  {analytics.spentPercentage.toFixed(1)}%
                </div>
                <div className='text-sm text-muted-foreground'>
                  Average Utilization
                </div>
              </div>

              <div className='text-center p-4 border rounded-lg'>
                <div className='text-2xl font-bold text-blue-600'>
                  {formatCurrency(analytics.totalRemaining, 'USD')}
                </div>
                <div className='text-sm text-muted-foreground'>
                  Total Remaining
                </div>
              </div>

              <div className='text-center p-4 border rounded-lg'>
                <div className='text-2xl font-bold text-purple-600'>
                  {budgets.filter((b) => b.has_affiliate).length}
                </div>
                <div className='text-sm text-muted-foreground'>
                  Affiliate Budgets
                </div>
              </div>
            </div>

            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Overall Budget Utilization</span>
                <span>{analytics.spentPercentage.toFixed(1)}%</span>
              </div>
              <Progress value={analytics.spentPercentage} className='h-3' />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
