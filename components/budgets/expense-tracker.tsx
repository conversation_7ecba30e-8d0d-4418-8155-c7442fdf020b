'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Plus, Receipt, Trash2, Upload } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { DatePicker } from '@/components/projects/date-picker';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { useBudgets } from '@/hooks/use-db';
import type { Budget } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/currency';

// Zod schema for expense form validation
const expenseFormSchema = z.object({
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  description: z.string().min(1, 'Description is required'),
  category: z.string().min(1, 'Category is required'),
  date: z.string().min(1, 'Date is required'),
  receipt_url: z.string().url().optional().or(z.literal('')),
});

type ExpenseFormData = z.infer<typeof expenseFormSchema>;

interface Expense {
  id: string;
  amount: number;
  description: string;
  category: string;
  date: string;
  receipt_url?: string;
  created_at: string;
}

interface ExpenseTrackerProps {
  budget: Budget;
  className?: string;
}

const expenseCategories = [
  'Software & Tools',
  'Hardware',
  'Marketing',
  'Travel',
  'Consulting',
  'Training',
  'Office Supplies',
  'Utilities',
  'Other',
];

export function ExpenseTracker({ budget, className }: ExpenseTrackerProps) {
  const { addExpense } = useBudgets();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Parse expenses from budget.expense_details
  const expenses: Expense[] = budget.expense_details || [];
  const totalExpenses = expenses.reduce(
    (sum, expense) => sum + expense.amount,
    0
  );
  const remainingBudget = budget.CurrentAmount;

  const form = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseFormSchema),
    defaultValues: {
      amount: 0,
      description: '',
      category: '',
      date: new Date().toISOString().split('T')[0],
      receipt_url: '',
    },
  });

  const onSubmit = async (data: ExpenseFormData) => {
    setIsSubmitting(true);

    try {
      // Check if expense would exceed remaining budget
      if (data.amount > remainingBudget) {
        toast.error('Expense amount exceeds remaining budget');
        return;
      }

      await addExpense(budget.id, {
        amount: data.amount,
        description: data.description,
        category: data.category,
        date: data.date,
        receipt_url: data.receipt_url || undefined,
      });

      toast.success('Expense added successfully');
      setIsAddDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error('Error adding expense:', error);
      toast.error(
        `Failed to add expense: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Software & Tools': 'bg-blue-100 text-blue-800',
      Hardware: 'bg-gray-100 text-gray-800',
      Marketing: 'bg-green-100 text-green-800',
      Travel: 'bg-purple-100 text-purple-800',
      Consulting: 'bg-orange-100 text-orange-800',
      Training: 'bg-yellow-100 text-yellow-800',
      'Office Supplies': 'bg-pink-100 text-pink-800',
      Utilities: 'bg-indigo-100 text-indigo-800',
      Other: 'bg-gray-100 text-gray-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='flex items-center gap-2'>
              <Receipt className='h-5 w-5' />
              Expense Tracking
            </CardTitle>
            <CardDescription>
              Track expenses against this budget
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size='sm' className='flex items-center gap-2'>
                <Plus className='h-4 w-4' />
                Add Expense
              </Button>
            </DialogTrigger>
            <DialogContent className='sm:max-w-[425px]'>
              <DialogHeader>
                <DialogTitle>Add New Expense</DialogTitle>
                <DialogDescription>
                  Add an expense to this budget. The remaining budget will be
                  updated automatically.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className='space-y-4'
                >
                  <FormField
                    control={form.control}
                    name='amount'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type='number'
                            min='0'
                            step='0.01'
                            placeholder='0.00'
                            disabled={isSubmitting}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Available:{' '}
                          {formatCurrency(remainingBudget, budget.Currency)}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='description'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description *</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder='Describe the expense...'
                            disabled={isSubmitting}
                            rows={2}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='category'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select category' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {expenseCategories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <Label>Date *</Label>
                    <div className='mt-2'>
                      <DatePicker
                        date={
                          form.watch('date')
                            ? new Date(form.watch('date'))
                            : undefined
                        }
                        onDateChange={(date) =>
                          form.setValue(
                            'date',
                            date ? date.toISOString().split('T')[0] : ''
                          )
                        }
                      />
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name='receipt_url'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Receipt URL</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type='url'
                            placeholder='https://...'
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormDescription>
                          Optional link to receipt or invoice
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className='flex gap-3 pt-4'>
                    <Button
                      type='submit'
                      disabled={isSubmitting}
                      className='flex-1'
                    >
                      {isSubmitting ? 'Adding...' : 'Add Expense'}
                    </Button>
                    <Button
                      type='button'
                      variant='outline'
                      onClick={() => setIsAddDialogOpen(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {/* Budget Summary */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-muted rounded-lg'>
          <div className='text-center'>
            <div className='text-2xl font-bold'>
              {formatCurrency(budget.ActualAmount, budget.Currency)}
            </div>
            <div className='text-sm text-muted-foreground'>Total Budget</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-red-600'>
              {formatCurrency(totalExpenses, budget.Currency)}
            </div>
            <div className='text-sm text-muted-foreground'>Total Expenses</div>
          </div>
          <div className='text-center'>
            <div
              className={cn(
                'text-2xl font-bold',
                remainingBudget < budget.ActualAmount * 0.2
                  ? 'text-red-600'
                  : 'text-green-600'
              )}
            >
              {formatCurrency(remainingBudget, budget.Currency)}
            </div>
            <div className='text-sm text-muted-foreground'>Remaining</div>
          </div>
        </div>

        {/* Expenses Table */}
        {expenses.length > 0 ? (
          <div className='border rounded-lg'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className='text-right'>Amount</TableHead>
                  <TableHead className='w-[50px]'></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {expenses
                  .sort(
                    (a, b) =>
                      new Date(b.date).getTime() - new Date(a.date).getTime()
                  )
                  .map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell className='font-medium'>
                        {formatDate(expense.date)}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className='font-medium'>
                            {expense.description}
                          </div>
                          {expense.receipt_url && (
                            <a
                              href={expense.receipt_url}
                              target='_blank'
                              rel='noopener noreferrer'
                              className='text-xs text-blue-600 hover:underline flex items-center gap-1'
                            >
                              <Upload className='h-3 w-3' />
                              View Receipt
                            </a>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn(
                            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                            getCategoryColor(expense.category)
                          )}
                        >
                          {expense.category}
                        </span>
                      </TableCell>
                      <TableCell className='text-right font-medium'>
                        {formatCurrency(expense.amount, budget.Currency)}
                      </TableCell>
                      <TableCell>
                        <Button
                          size='sm'
                          variant='ghost'
                          className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
                          onClick={() => {
                            toast.info(
                              'Expense deletion will be available in the next update'
                            );
                          }}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className='text-center py-8 text-muted-foreground'>
            <Receipt className='h-12 w-12 mx-auto mb-4 opacity-50' />
            <p>No expenses recorded yet</p>
            <p className='text-sm'>Add your first expense to start tracking</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
