'use client';

import { format } from 'date-fns';
import {
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Mail,
  Phone,
  User,
  XCircle,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useProposals } from '@/hooks/use-db';
import type { Proposal } from '@/lib/supabase/database-modules';
import { formatCurrency } from '@/lib/utils/currency';

interface ReferralDetailsProps {
  referralId: number;
}

export function ReferralDetails({ referralId }: ReferralDetailsProps) {
  const { fetchProposal } = useProposals(true); // Use affiliate filtering
  const [proposal, setProposal] = useState<Proposal | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProposal = () => {
      setLoading(true);
      setError(null);

      fetchProposal(referralId)
        .then((data) => {
          setProposal(data);
        })
        .catch((err) => {
          setError(err.message || 'Failed to load referral details');
        })
        .finally(() => {
          setLoading(false);
        });
    };

    loadProposal();
  }, [referralId, fetchProposal]);

  if (loading) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <div className='h-6 w-48 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
            <div className='h-4 w-32 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
          </CardHeader>
          <CardContent className='space-y-4'>
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className='h-4 w-full bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse'
              />
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !proposal) {
    return (
      <div className='text-center py-12'>
        <p className='text-destructive font-medium'>
          {error || 'Referral not found'}
        </p>
        <p className='text-sm text-muted-foreground mt-2'>
          Please check the referral ID and try again.
        </p>
      </div>
    );
  }

  const getStatusInfo = () => {
    if (proposal.is_approved === true) {
      return {
        label: 'Approved',
        variant: 'default' as const,
        icon: <CheckCircle className='h-4 w-4' />,
        className:
          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      };
    }
    if (proposal.is_approved === false) {
      return {
        label: 'Rejected',
        variant: 'destructive' as const,
        icon: <XCircle className='h-4 w-4' />,
        className: '',
      };
    }
    return {
      label: 'Pending Review',
      variant: 'secondary' as const,
      icon: <Clock className='h-4 w-4' />,
      className: '',
    };
  };

  const statusInfo = getStatusInfo();
  const clientName =
    proposal.client_name ||
    proposal.affiliate_proposal?.client_name ||
    'Unknown Client';
  const clientEmail = proposal.client_email;

  return (
    <div className='space-y-6'>
      {/* Status and Overview */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div>
              <CardTitle className='flex items-center space-x-2'>
                <User className='h-5 w-5' />
                <span>{clientName}</span>
              </CardTitle>
              <CardDescription>
                Referral submitted on{' '}
                {format(new Date(proposal.created_at), 'MMMM dd, yyyy')}
              </CardDescription>
            </div>
            <Badge
              variant={statusInfo.variant}
              className={statusInfo.className}
            >
              {statusInfo.icon}
              <span className='ml-1'>{statusInfo.label}</span>
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className='grid gap-6 md:grid-cols-2'>
            <div className='space-y-3'>
              <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                Timeline
              </h4>
              <div className='space-y-2'>
                <div className='flex items-center space-x-2 text-sm'>
                  <Calendar className='h-4 w-4 text-muted-foreground' />
                  <span className='text-muted-foreground'>Created:</span>
                  <span className='font-medium'>
                    {format(
                      new Date(proposal.created_at),
                      'MMM dd, yyyy HH:mm'
                    )}
                  </span>
                </div>
              </div>
            </div>
            <div className='space-y-3'>
              <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                Status
              </h4>
              <div className='space-y-2'>
                <div className='flex items-center space-x-2 text-sm'>
                  <span className='text-muted-foreground'>Current Status:</span>
                  <span className='font-medium'>{statusInfo.label}</span>
                </div>
                <div className='flex items-center space-x-2 text-sm'>
                  <span className='text-muted-foreground'>Completed:</span>
                  <span className='font-medium'>
                    {proposal.completed ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Client Details Section */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <User className='h-5 w-5' />
            <span>Client Details</span>
          </CardTitle>
          <CardDescription>
            Information about the client being referred
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid gap-6 md:grid-cols-2'>
            <div className='space-y-3'>
              <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                Contact Information
              </h4>
              <div className='space-y-2'>
                <div className='flex items-center space-x-2 text-sm'>
                  <User className='h-4 w-4 text-muted-foreground' />
                  <span className='text-muted-foreground'>Name:</span>
                  <span className='font-medium'>{clientName}</span>
                </div>
                {clientEmail && (
                  <div className='flex items-center space-x-2 text-sm'>
                    <Mail className='h-4 w-4 text-muted-foreground' />
                    <span className='text-muted-foreground'>Email:</span>
                    <span className='font-medium'>{clientEmail}</span>
                  </div>
                )}
                {proposal.client_phone && (
                  <div className='flex items-center space-x-2 text-sm'>
                    <Phone className='h-4 w-4 text-muted-foreground' />
                    <span className='text-muted-foreground'>Phone:</span>
                    <span className='font-medium'>{proposal.client_phone}</span>
                  </div>
                )}
              </div>
            </div>
            {proposal.client_description && (
              <div className='space-y-3'>
                <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                  Client Needs
                </h4>
                <div className='prose prose-sm max-w-none dark:prose-invert'>
                  <p className='whitespace-pre-wrap text-sm'>
                    {proposal.client_description}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Proposal Details Section */}
      {proposal.affiliate_proposal && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center space-x-2'>
              <FileText className='h-5 w-5' />
              <span>Proposal Details</span>
            </CardTitle>
            <CardDescription>
              Details about the type of work and proposed approach
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid gap-6 md:grid-cols-2'>
              <div className='space-y-3'>
                <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                  Work Type
                </h4>
                <div className='space-y-2'>
                  <div className='flex items-center space-x-2 text-sm'>
                    <span className='text-muted-foreground'>
                      Proposal Type:
                    </span>
                    <span className='font-medium'>
                      {proposal.affiliate_proposal.proposal_type}
                    </span>
                  </div>
                </div>
              </div>
              <div className='space-y-3'>
                <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                  Reference
                </h4>
                <div className='space-y-2'>
                  <div className='flex items-center space-x-2 text-sm'>
                    <span className='text-muted-foreground'>Client Name:</span>
                    <span className='font-medium'>
                      {proposal.affiliate_proposal.client_name}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {proposal.affiliate_proposal.proposal_message && (
              <div className='space-y-3 mt-6'>
                <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                  Proposal Message
                </h4>
                <div className='prose prose-sm max-w-none dark:prose-invert'>
                  <div className='bg-muted/50 rounded-lg p-4'>
                    <p className='whitespace-pre-wrap text-sm leading-relaxed'>
                      {proposal.affiliate_proposal.proposal_message}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Show proposed budget if available */}
            {(proposal.proposed_budget ||
              proposal.affiliate_proposal.proposed_budget) && (
              <div className='space-y-3 mt-6'>
                <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                  Proposed Budget
                </h4>
                <div className='bg-muted/50 rounded-lg p-4'>
                  <div className='flex items-center space-x-2'>
                    <span className='text-2xl font-bold text-green-600'>
                      {formatCurrency(
                        proposal.proposed_budget ||
                          proposal.affiliate_proposal.proposed_budget ||
                          0,
                        'USD'
                      )}
                    </span>
                  </div>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Estimated project budget as proposed by the affiliate
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
