import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ProposalApproved } from '@/lib/emails/proposals/approved';
import type { ProposalApprovedEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request): Promise<NextResponse> {
  return new Promise<NextResponse>((resolve, reject) => {
    let emailData: ProposalApprovedEmailData;

    request
      .json()
      .then((data: ProposalApprovedEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.proposal ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: proposal or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = ProposalApproved(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(ProposalApproved(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Proposal Approved: ${emailData.proposal.title}`,
          react: ProposalApproved(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Proposal approved email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Proposal Approved: ${emailData.proposal.title}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Proposal approved email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error: error.message || 'Failed to send proposal approved email',
          })
        );
      });
  });
}
