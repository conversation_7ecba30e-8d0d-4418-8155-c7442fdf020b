import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { IssueHealthUpdate } from '@/lib/emails/issues/health-updated';
import type { IssueHealthUpdateEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request): Promise<NextResponse> {
  return new Promise<NextResponse>((resolve, reject) => {
    let emailData: IssueHealthUpdateEmailData;

    request
      .json()
      .then((data: IssueHealthUpdateEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.issue ||
          !emailData.project ||
          !emailData.oldHealth ||
          !emailData.newHealth ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: issue, project, oldHealth, newHealth, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = IssueHealthUpdate(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(IssueHealthUpdate(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Issue Health Updated: ${emailData.issue.title}`,
          react: IssueHealthUpdate(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Issue health update email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Issue Health Updated: ${emailData.issue.title}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Issue health update email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error: error.message || 'Failed to send issue health update email',
          })
        );
      });
  });
}
