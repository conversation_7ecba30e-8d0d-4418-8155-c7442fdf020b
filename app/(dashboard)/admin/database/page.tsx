'use client';

import { Building2, User<PERSON>he<PERSON> } from 'lucide-react';
import Link from 'next/link';
import { DatabaseMetrics } from '@/components/database/database-metrics';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function DatabasePage() {
  const databaseTables = [
    {
      name: 'Clients',
      description: 'Client records and contact information',
      icon: Building2,
      href: '/admin/database/clients',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    },
    {
      name: 'Waitlists',
      description: 'Application submissions and waitlist management',
      icon: UserCheck,
      href: '/admin/database/waitlists',
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-950/20',
    },
  ];

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Database Management</p>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        {/* Database Overview Metrics */}
        <DatabaseMetrics />

        {/* Database Tables Overview */}
        <div className='space-y-4'>
          <div>
            <h2 className='text-lg font-semibold'>Database Tables</h2>
            <p className='text-sm text-muted-foreground'>
              Manage and view data across all database tables
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            {databaseTables.map((table) => {
              const Icon = table.icon;
              return (
                <Card
                  key={table.name}
                  className='hover:shadow-md transition-shadow'
                >
                  <CardHeader className='pb-3'>
                    <div className='flex items-center space-x-3'>
                      <div className={`p-2 rounded-lg ${table.bgColor}`}>
                        <Icon className={`h-5 w-5 ${table.color}`} />
                      </div>
                      <div>
                        <CardTitle className='text-base'>
                          {table.name}
                        </CardTitle>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className='pt-0'>
                    <CardDescription className='mb-4'>
                      {table.description}
                    </CardDescription>
                    <Link href={table.href}>
                      <Button variant='outline' size='sm' className='w-full'>
                        Manage {table.name}
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>
    </main>
  );
}
