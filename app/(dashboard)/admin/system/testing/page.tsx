'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { SystemIntegrationTest } from '@/components/testing/system-integration-test';
import { SystemDocumentation } from '@/components/documentation/system-documentation';

export default function SystemTestingPage() {
  return (
    <main className="h-full flex flex-col">
      <header className="pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full">
        <p className="font-medium text-sm">System Testing & Documentation</p>
      </header>
      <section className="flex-1 p-6">
        <Tabs defaultValue="testing" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="testing">Integration Testing</TabsTrigger>
            <TabsTrigger value="documentation">Documentation</TabsTrigger>
          </TabsList>
          
          <TabsContent value="testing" className="space-y-6 mt-6">
            <SystemIntegrationTest />
          </TabsContent>
          
          <TabsContent value="documentation" className="space-y-6 mt-6">
            <SystemDocumentation />
          </TabsContent>
        </Tabs>
      </section>
    </main>
  );
}
