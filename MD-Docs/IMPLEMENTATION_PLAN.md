# useProjects Hook Implementation Plan

## Current State Analysis

### Existing Database Tables
✅ **Already Exists:**
- `projects` table with basic structure (id, name, description, status, priority, etc.)
- `status` table for project statuses
- `priorities` table for project priorities  
- `labels` table for project labels
- `teams` table for team management
- `team_members` table for team membership
- `project_teams` table for project-team relationships

### Current useProjects Hook (hooks/use-db.ts lines 910-1033)
✅ **Current Implementation:**
- Basic fetch functionality with real-time subscriptions
- Simple Project interface (lines 885-907)
- Read-only operations (no CRUD methods)
- Basic error handling and loading states

❌ **Missing Features:**
- CRUD operations (create, update, delete)
- Optimistic updates
- Advanced filtering and search
- Project member management
- Health status management
- Lead assignment functionality
- Progress tracking utilities

## Target State (Based on Documentation)

### Required Database Schema Updates

#### 1. Missing Tables to Create
```sql
-- Health status reference table
CREATE TABLE health_status (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  description TEXT,
  sort_order INTEGER DEFAULT 0
);

-- Project members junction table  
CREATE TABLE project_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member' CHECK (role IN ('lead', 'member', 'viewer')),
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(project_id, user_id)
);
```

#### 2. Projects Table Updates Needed
Current projects table needs these modifications:
- Change `id` from UUID to TEXT (for better compatibility)
- Add foreign key constraints for status_id, priority_id, health_id
- Add proper indexes for performance
- Update RLS policies

### Hook Enhancement Requirements

#### 1. Interface Updates
- Enhance Project interface to match documentation
- Add User, Status, Priority, HealthStatus interfaces
- Add CreateProjectInput and FilterOptions interfaces

#### 2. CRUD Operations to Add
- `addProject(input: CreateProjectInput): Promise<Project>`
- `updateProject(id: string, updates: Partial<Project>): Promise<Project>`
- `deleteProject(id: string): Promise<void>`
- `duplicateProject(id: string, name?: string): Promise<Project>`

#### 3. Advanced Features to Implement
- `searchProjects(query: string): Project[]`
- `filterProjects(options: FilterOptions): Project[]`
- `addProjectMember(projectId: string, userId: string, role?: string): Promise<void>`
- `removeProjectMember(projectId: string, userId: string): Promise<void>`
- `updateProjectHealth(id: string, healthId: string): Promise<void>`
- `updateProjectProgress(id: string, percent: number): Promise<void>`

## Implementation Steps

### Phase 1: Database Schema Setup
1. ✅ Analyze current database structure
2. 🔄 Create missing tables (health_status, project_members)
3. 🔄 Add proper indexes and constraints
4. 🔄 Set up RLS policies
5. 🔄 Populate reference data (health_status)

### Phase 2: Hook Interface Enhancement  
1. 🔄 Update Project interface to match documentation
2. 🔄 Add supporting interfaces (User, Status, Priority, etc.)
3. 🔄 Add CreateProjectInput and FilterOptions interfaces

### Phase 3: Core CRUD Implementation
1. 🔄 Implement addProject with optimistic updates
2. 🔄 Implement updateProject with optimistic updates  
3. 🔄 Implement deleteProject with optimistic updates
4. 🔄 Add comprehensive error handling

### Phase 4: Advanced Features
1. 🔄 Implement search and filtering
2. 🔄 Add project member management
3. 🔄 Add health status management
4. 🔄 Add progress tracking utilities

### Phase 5: Component Migration
1. 🔄 Update existing project components to use enhanced hook
2. 🔄 Test all functionality
3. 🔄 Update TypeScript types generation

## Risk Assessment

### High Risk
- Database schema changes could break existing functionality
- Changing Project interface might break existing components

### Medium Risk  
- Real-time subscriptions complexity
- Optimistic updates rollback logic

### Low Risk
- Adding new CRUD methods
- Enhanced filtering and search

## Migration Strategy

### Backward Compatibility
- Keep existing basic functionality working
- Add new features incrementally
- Maintain existing Project interface initially
- Gradually migrate components to use new features

### Testing Approach
- Test each phase independently
- Verify real-time updates work correctly
- Test optimistic updates and rollback scenarios
- Validate RLS policies work as expected

## Next Immediate Steps

1. **Create missing database tables** using Thehuefactory Supabase MCP tool
2. **Update Project interface** in use-db.ts to match documentation
3. **Implement basic CRUD operations** with Promise-based architecture
4. **Add real-time subscription enhancements**
5. **Test with existing components**

## Files to Modify

### Primary Files
- `hooks/use-db.ts` - Main hook implementation
- Database schema via Supabase MCP tool

### Secondary Files (Later)
- `components/projects/projects.tsx` - Update to use enhanced hook
- Any other components using the projects hook

## Success Criteria

✅ **Phase 1 Complete When:**
- All required database tables exist
- Proper indexes and constraints are in place
- RLS policies are configured

✅ **Phase 2 Complete When:**
- Project interface matches documentation
- All supporting interfaces are defined
- TypeScript compilation succeeds

✅ **Phase 3 Complete When:**
- All CRUD operations work correctly
- Optimistic updates function properly
- Error handling is comprehensive

✅ **Final Success When:**
- All documentation features are implemented
- Existing components work without modification
- Real-time updates work across all operations
- Performance is acceptable with proper indexing
