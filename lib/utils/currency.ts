/**
 * Currency utility module for handling multi-currency operations
 * Provides formatting, conversion display, and supported currencies management
 */

export interface Currency {
  code: string;
  name: string;
  symbol: string;
  locale: string;
  decimals: number;
}

export interface CurrencyConversion {
  from: string;
  to: string;
  rate: number;
  lastUpdated: string;
}

// Supported currencies with their display information
export const SUPPORTED_CURRENCIES: Currency[] = [
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    locale: 'en-US',
    decimals: 2,
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    locale: 'de-DE',
    decimals: 2,
  },
  {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    locale: 'en-GB',
    decimals: 2,
  },
  {
    code: 'JPY',
    name: 'Japanese Yen',
    symbol: '¥',
    locale: 'ja-JP',
    decimals: 0,
  },
  {
    code: 'CAD',
    name: 'Canadian Dollar',
    symbol: 'C$',
    locale: 'en-CA',
    decimals: 2,
  },
  {
    code: 'AUD',
    name: 'Australian Dollar',
    symbol: 'A$',
    locale: 'en-AU',
    decimals: 2,
  },
  {
    code: 'CHF',
    name: 'Swiss Franc',
    symbol: 'CHF',
    locale: 'de-CH',
    decimals: 2,
  },
  {
    code: 'CNY',
    name: 'Chinese Yuan',
    symbol: '¥',
    locale: 'zh-CN',
    decimals: 2,
  },
  {
    code: 'INR',
    name: 'Indian Rupee',
    symbol: '₹',
    locale: 'en-IN',
    decimals: 2,
  },
];

// Default currency
export const DEFAULT_CURRENCY = 'USD';

/**
 * Get currency information by code
 */
export function getCurrency(code: string): Currency | null {
  return SUPPORTED_CURRENCIES.find((c) => c.code === code) || null;
}

/**
 * Format amount with currency symbol and proper locale formatting
 */
export function formatCurrency(
  amount: number | string,
  currencyCode: string = DEFAULT_CURRENCY,
  options: {
    showSymbol?: boolean;
    showCode?: boolean;
    compact?: boolean;
  } = {}
): string {
  const { showSymbol = true, showCode = false, compact = false } = options;

  const currency = getCurrency(currencyCode);
  if (!currency) {
    // Fallback for unsupported currencies
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (Number.isNaN(numAmount)) return '0.00';

    return `${showSymbol ? `${currencyCode} ` : ''}${numAmount.toLocaleString(
      'en-US',
      {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }
    )}${showCode ? ` ${currencyCode}` : ''}`;
  }

  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (Number.isNaN(numAmount))
    return `${showSymbol ? currency.symbol : ''}0${currency.decimals > 0 ? '.'.padEnd(currency.decimals + 1, '0') : ''}`;

  // Format with proper locale and decimals
  const formatted = numAmount.toLocaleString(currency.locale, {
    minimumFractionDigits: currency.decimals,
    maximumFractionDigits: currency.decimals,
    notation: compact ? 'compact' : 'standard',
  });

  // Build the final string
  let result = '';
  if (showSymbol) {
    result += currency.symbol;
  }
  result += formatted;
  if (showCode) {
    result += ` ${currency.code}`;
  }

  return result;
}

/**
 * Format currency for display in tables and lists
 */
export function formatCurrencyCompact(
  amount: number | string,
  currencyCode: string = DEFAULT_CURRENCY
): string {
  return formatCurrency(amount, currencyCode, { compact: true });
}

/**
 * Format currency with both symbol and code for clarity
 */
export function formatCurrencyFull(
  amount: number | string,
  currencyCode: string = DEFAULT_CURRENCY
): string {
  return formatCurrency(amount, currencyCode, {
    showSymbol: true,
    showCode: true,
  });
}

/**
 * Parse currency string to number
 */
export function parseCurrency(value: string): number {
  // Remove all non-numeric characters except decimal point and minus sign
  const cleaned = value.replace(/[^\d.-]/g, '');
  const parsed = parseFloat(cleaned);
  return Number.isNaN(parsed) ? 0 : parsed;
}

/**
 * Validate currency code
 */
export function isValidCurrency(code: string): boolean {
  return SUPPORTED_CURRENCIES.some((c) => c.code === code);
}

/**
 * Get currency options for dropdowns
 */
export function getCurrencyOptions(): Array<{ value: string; label: string }> {
  return SUPPORTED_CURRENCIES.map((currency) => ({
    value: currency.code,
    label: `${currency.symbol} ${currency.name} (${currency.code})`,
  }));
}

/**
 * Convert amount display with exchange rate info
 * Note: This is for display purposes only. Actual conversion rates should come from a real API.
 */
export function formatCurrencyWithConversion(
  amount: number | string,
  fromCurrency: string,
  toCurrency: string = DEFAULT_CURRENCY,
  exchangeRate?: number
): string {
  const primaryFormat = formatCurrency(amount, fromCurrency);

  if (fromCurrency === toCurrency || !exchangeRate) {
    return primaryFormat;
  }

  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (Number.isNaN(numAmount)) return primaryFormat;

  const convertedAmount = numAmount * exchangeRate;
  const convertedFormat = formatCurrency(convertedAmount, toCurrency);

  return `${primaryFormat} (≈ ${convertedFormat})`;
}

/**
 * Get currency symbol only
 */
export function getCurrencySymbol(currencyCode: string): string {
  const currency = getCurrency(currencyCode);
  return currency?.symbol || currencyCode;
}

/**
 * Format amount for input fields (no symbol, proper decimals)
 */
export function formatCurrencyInput(
  amount: number | string,
  currencyCode: string = DEFAULT_CURRENCY
): string {
  const currency = getCurrency(currencyCode);
  const decimals = currency?.decimals || 2;

  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (Number.isNaN(numAmount)) return '';

  return numAmount.toFixed(decimals);
}

/**
 * Utility for React components to get currency formatting props
 */
export function getCurrencyProps(currencyCode: string = DEFAULT_CURRENCY) {
  const currency = getCurrency(currencyCode);

  return {
    currency,
    symbol: currency?.symbol || currencyCode,
    decimals: currency?.decimals || 2,
    locale: currency?.locale || 'en-US',
    format: (amount: number | string) => formatCurrency(amount, currencyCode),
    formatCompact: (amount: number | string) =>
      formatCurrencyCompact(amount, currencyCode),
    formatFull: (amount: number | string) =>
      formatCurrencyFull(amount, currencyCode),
    formatInput: (amount: number | string) =>
      formatCurrencyInput(amount, currencyCode),
  };
}
