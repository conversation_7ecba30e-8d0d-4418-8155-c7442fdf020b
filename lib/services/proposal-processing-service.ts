import type {
  Budget,
  Client,
  CreateBudgetInput,
  CreateClientInput,
  CreateProjectInput,
  Project,
  Proposal,
  ProposalProcessingResult,
  ProposalValidation,
  ReferalProposalType,
} from '@/lib/supabase/database-modules';

/**
 * Comprehensive proposal processing service
 * Handles the complete workflow from proposal approval to client/project/budget creation
 */
export const ProposalProcessingService = {
  validateAndExtractProposalData: (proposal: Proposal): ProposalValidation => {
    const errors: string[] = [];
    const warnings: string[] = [];

    const proposalData: ReferalProposalType | null =
      proposal.affiliate_proposal;

    // Extract client data
    const clientData: Partial<CreateClientInput> = {
      name:
        proposal.client_name || proposalData?.client_name || 'Unknown Client',
      email: proposal.client_email || null,
      phone: proposal.client_phone || null,
      description: proposal.client_description || null,
      created_by: proposal.user_id,
    };

    // Extract project data
    const projectName =
      proposalData?.proposal_type || `Project for ${clientData.name}`;
    const projectData: Partial<CreateProjectInput> = {
      name: projectName,
      description: proposalData?.proposal_message || null,
      is_proposed: true,
      affiliate_user: proposal.user_id,
    };

    // Extract budget data - check both proposal.proposed_budget and proposalData.proposed_budget
    const budgetAmount =
      proposal.proposed_budget || proposalData?.proposed_budget || 0;

    // Use default dates for budget
    const startDate = new Date().toISOString();
    const endDate =
      ProposalProcessingService.calculateDefaultEndDate(undefined);

    const budgetData: Partial<CreateBudgetInput> = {
      ActualAmount: budgetAmount,
      CurrentAmount: budgetAmount,
      Currency: 'USD',
      Category:
        ProposalProcessingService.inferCategoryFromProposal(proposalData),
      Status: 'draft',
      has_affiliate: true,
      affiliateId: proposal.user_id,
      AffiliateCommission: budgetAmount > 0 ? budgetAmount * 0.1 : 0, // 10% commission if budget exists
      StartDate: startDate,
      EndDate: endDate,
      Notes: `Created from proposal ${proposal.id} - ${proposalData?.proposal_type || 'referral'}${budgetAmount > 0 ? ` (Budget: $${budgetAmount})` : ''}`,
    };

    // Validation rules
    if (!clientData.name) {
      errors.push('Client name is required');
    }

    if (budgetAmount && budgetAmount <= 0) {
      errors.push('Budget amount must be positive');
    }

    if (budgetData.StartDate && budgetData.EndDate) {
      const startDate = new Date(budgetData.StartDate);
      const endDate = new Date(budgetData.EndDate);
      if (endDate <= startDate) {
        errors.push('End date must be after start date');
      }
    }

    // Warnings for missing optional data
    if (!clientData.email) {
      warnings.push('No client email provided - communication may be limited');
    }

    if (!budgetAmount) {
      warnings.push(
        'No budget information provided - budget tracking will not be available'
      );
    }

    if (!projectName) {
      warnings.push(
        'No project name provided - project will need to be created manually'
      );
    }

    // Always add timeline warning for basic referral proposals
    warnings.push(
      'No timeline provided - using default 90-day project duration'
    );

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      extractedData: {
        clientData,
        projectData,
        budgetData,
      },
    };
  },

  processApprovedProposal: async (
    proposal: Proposal,
    hooks: {
      addClient: (input: CreateClientInput) => Promise<Client>;
      fetchClient: (emailOrId: string) => Promise<Client | null>;
      addProject: (input: CreateProjectInput) => Promise<Project>;
      addBudget: (input: CreateBudgetInput) => Promise<Budget>;
    }
  ): Promise<ProposalProcessingResult> => {
    const result: ProposalProcessingResult = {
      success: false,
      errors: [],
      warnings: [],
      summary: '',
    };

    try {
      // Step 1: Validate proposal data
      const validation =
        ProposalProcessingService.validateAndExtractProposalData(proposal);
      result.warnings = validation.warnings;

      if (!validation.isValid) {
        result.errors = validation.errors;
        result.summary = `Validation failed: ${validation.errors.join(', ')}`;
        return result;
      }

      const { clientData, projectData, budgetData } = validation.extractedData;

      // Step 2: Create or find existing client
      let client: Client;

      if (clientData.email) {
        const existingClient = await hooks.fetchClient(clientData.email);
        if (existingClient) {
          client = existingClient;
          result.warnings.push(`Using existing client: ${client.name}`);
        } else {
          client = await hooks.addClient(clientData as CreateClientInput);
        }
      } else {
        client = await hooks.addClient(clientData as CreateClientInput);
      }

      result.client = client;

      // Step 3: Create project (always create a project for approved proposals)
      const projectInput: CreateProjectInput = {
        ...projectData,
        client_id: client.id,
      } as CreateProjectInput;

      const project = await hooks.addProject(projectInput);
      result.project = project;

      // Step 4: Handle budget creation/update
      if (project.budget_id) {
        // Budget was automatically created by database trigger
        const proposedAmount = budgetData.ActualAmount || 0;
        if (proposedAmount > 0) {
          result.warnings.push(
            `Budget auto-created by trigger. Proposed amount: $${proposedAmount} (Note: Budget update functionality needs to be implemented)`
          );
        } else {
          result.warnings.push(
            'Budget was automatically created by database trigger with default values'
          );
        }
      } else if (budgetData.ActualAmount && budgetData.ActualAmount > 0) {
        // Fallback: Create budget manually if no automatic budget was created
        const budgetInput: CreateBudgetInput = {
          ...budgetData,
          ProjectId: project.id,
          ClientId: client.id,
        } as CreateBudgetInput;

        const budget = await hooks.addBudget(budgetInput);
        result.budget = budget;
      }

      // Generate summary
      const proposedAmount = budgetData.ActualAmount || 0;
      const summaryParts = [
        `✅ Client: ${client.name}`,
        result.project ? `✅ Project: ${result.project.name}` : '',
        result.budget
          ? `✅ Budget: ${budgetData.Currency}${result.budget.ActualAmount}`
          : project.budget_id && proposedAmount > 0
            ? `✅ Budget: Auto-created with proposed amount $${proposedAmount}`
            : project.budget_id
              ? `✅ Budget: Auto-created (default amount)`
              : '',
      ].filter(Boolean);

      result.success = true;
      result.summary = summaryParts.join('\n');

      return result;
    } catch (error) {
      const errorDetails = {
        message:
          error instanceof Error ? error.message : 'Unknown processing error',
        error: error,
        stack: error instanceof Error ? error.stack : undefined,
        proposal: {
          id: proposal.id,
          user_id: proposal.user_id,
          client_name: proposal.client_name,
        },
      };
      console.error('Error processing approved proposal:', errorDetails);
      result.errors.push(errorDetails.message);
      result.summary = `Processing failed: ${result.errors.join(', ')}`;
      return result;
    }
  },

  inferCategoryFromProposal: (
    proposalData: ReferalProposalType | null
  ): 'marketing' | 'development' | 'consulting' | 'operations' | 'other' => {
    const text = (
      proposalData?.proposal_message ||
      proposalData?.proposal_type ||
      ''
    ).toLowerCase();

    if (
      text.includes('marketing') ||
      text.includes('advertising') ||
      text.includes('promotion')
    ) {
      return 'marketing';
    }
    if (
      text.includes('development') ||
      text.includes('coding') ||
      text.includes('programming') ||
      text.includes('website') ||
      text.includes('app')
    ) {
      return 'development';
    }
    if (
      text.includes('consulting') ||
      text.includes('advice') ||
      text.includes('strategy') ||
      text.includes('planning')
    ) {
      return 'consulting';
    }
    if (
      text.includes('operations') ||
      text.includes('management') ||
      text.includes('process')
    ) {
      return 'operations';
    }

    return 'other';
  },

  calculateDefaultEndDate: (timeline?: string): string => {
    const now = new Date();
    let daysToAdd = 90; // Default 90 days

    if (timeline) {
      const timelineText = timeline.toLowerCase();
      if (timelineText.includes('week')) {
        const weeks =
          ProposalProcessingService.extractNumber(timelineText) || 4;
        daysToAdd = weeks * 7;
      } else if (timelineText.includes('month')) {
        const months =
          ProposalProcessingService.extractNumber(timelineText) || 3;
        daysToAdd = months * 30;
      } else if (timelineText.includes('day')) {
        daysToAdd = ProposalProcessingService.extractNumber(timelineText) || 90;
      }
    }

    const endDate = new Date(now.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
    return endDate.toISOString().split('T')[0];
  },

  extractNumber: (text: string): number | null => {
    const match = text.match(/\d+/);
    return match ? parseInt(match[0], 10) : null;
  },

  generateProcessingReport: (
    proposal: Proposal,
    result: ProposalProcessingResult
  ): string => {
    const report = [
      `=== Proposal Processing Report ===`,
      `Proposal ID: ${proposal.id}`,
      `Client: ${proposal.client_name}`,
      `Affiliate: ${proposal.user_email}`,
      `Status: ${result.success ? 'SUCCESS' : 'FAILED'}`,
      ``,
      `Results:`,
      result.client
        ? `✅ Client Created: ${result.client.name} (${result.client.id})`
        : '❌ No client created',
      result.project
        ? `✅ Project Created: ${result.project.name} (${result.project.id})`
        : '⚠️ No project created',
      result.budget
        ? `✅ Budget Created: ${result.budget.Currency}${result.budget.ActualAmount} (${result.budget.id})`
        : '⚠️ No budget created',
      ``,
      result.warnings.length > 0
        ? `Warnings:\n${result.warnings.map((w) => `⚠️ ${w}`).join('\n')}`
        : '',
      result.errors.length > 0
        ? `Errors:\n${result.errors.map((e) => `❌ ${e}`).join('\n')}`
        : '',
      ``,
      `Summary: ${result.summary}`,
      `=================================`,
    ]
      .filter(Boolean)
      .join('\n');

    return report;
  },
};
