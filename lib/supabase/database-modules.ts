import type { LucideIcon } from 'lucide-react';
import type { Database } from './database-types';

type WorkTypes =
  | 'Graphic Design'
  | 'Website Development'
  | 'App Development'
  | 'Brand Development';

type VolunteerAreaTypes =
  | 'Design (Graphic/Web)'
  | 'Content Creation (Social Media, Blog)'
  | 'Event Support'
  | 'Community Engagement & Outreach'
  | 'Branding Strategy'
  | 'Photography/Videography'
  | 'Other (Please Specify)';

type VolunteerAvailabilityTypes =
  | 'Less than 5 hours'
  | '5-10 hours'
  | '10-15 hours'
  | '15+ hours';

type VolunteerDaysAvailabilityTypes =
  | 'Monday'
  | 'Tuesday'
  | 'Wednesday'
  | 'Thursday'
  | 'Friday'
  | 'Saturday'
  | 'Sunday';

type VolunteerTimeOfTheDayAvailabilityTypes =
  | 'Morning'
  | 'Afternoon'
  | 'Evening';

export type ReferalProposalType = {
  // Core proposal fields
  proposal_type: string | WorkTypes;
  client_name: string;
  proposal_message: string;
  proposed_budget?: number | null;

  // Extended client fields (optional)
  company_name?: string;
  client_email?: string;
  contact_email?: string;
  client_phone?: string;
  contact_phone?: string;
  client_description?: string;

  // Extended project fields (optional)
  project_name?: string;
  title?: string;
  description?: string;
  project_description?: string;

  // Budget and financial fields (optional)
  budget?: number;
  estimated_cost?: number;
  currency?: string;
  category?: string;
  commission?: number;

  // Timeline fields (optional)
  start_date?: string;
  end_date?: string;
  timeline?: string;

  // Allow additional fields for flexibility
  [key: string]: any;
};

export type VolunteerDetailsType = {
  dob: string;
  about_you: string;
  interest_in_volunteering: string;
  relivant_skills: string;
  contribution_area: string | VolunteerAreaTypes;
  reason_for_joining: string;
  past_volunteering: string;
  hours_per_week: VolunteerAvailabilityTypes;
  days_per_week: VolunteerDaysAvailabilityTypes[];
  prefered_time_of_day: VolunteerTimeOfTheDayAvailabilityTypes;
  access_to_equipments: boolean;
  other_details: string;
  subscribe_to_newsletter: boolean;
};

export type Profile_Types = Database['public']['Tables']['profiles']['Row'];
export type JoinUsTable_Types =
  Database['public']['Tables']['JOIN_US_TABLE']['Row'];
export type UserRole = Database['public']['Enums']['Role Types'];

// Portfolio-related types
export interface PortfolioProfile extends Profile_Types {
  join_us_data?: JoinUsTable_Types;
}

export interface PortfolioData {
  // Basic profile information
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  status: string | null;
  role: UserRole;
  joined_date: string | null;

  // Professional information from JOIN_US_TABLE
  full_name: string | null;
  phone: string | null;
  location: string | null;
  position: string | null;
  position_other: string | null;
  skills: string | null;
  interests: string | null;
  past_experience: string | null;
  why_join: string | null;
  hours_per_week: string | null;
  available_days: string[] | null;
  preferred_time: string | null;
  equipment: string | null;
  areas: string[] | null;
  other_area: string | null;
  additional_info: string | null;
  resume_url: string | null;
  dob: string | null;
  message: string | null;

  // NDA and approval status
  is_nda_signed: boolean | null;
  nda_signed_date: string | null;
  approved: Database['public']['Enums']['is_accepted'];
  reviewed: Database['public']['Enums']['is_reviewed'];

  // Metadata
  created_at: string;
  updated_at: string | null;
}

export interface User {
  name: Profile_Types['full_name'];
  email: Profile_Types['email'];
  avatar: string;
  role: Profile_Types['role'];
}

export interface SidebarData {
  user: User;
  teams: Array<{
    name: string;
    logo: LucideIcon;
    plan: string;
  }>;
  navHeader: Array<{
    title: string;
    url: string;
    icon: LucideIcon;
  }>;
  navMain: Array<{
    title: string;
    items?: Array<{
      icon: LucideIcon;
      title: string;
      isActive?: boolean;
      url: string;
      items?: Array<{
        icon: LucideIcon;
        title: string;
        url: string;
      }>;
    }>;
  }>;
}

// ============================================================================
// Issues Management System Types
// ============================================================================

// Base types for issues system
export interface IssueUser {
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  status: 'online' | 'offline' | 'away';
  role: 'Member' | 'Admin' | 'Guest';
  joined_date: string;
}

export interface IssueStatus {
  id: string;
  name: string;
  color: string;
  icon_name: string | null;
  sort_order: number;
}

export interface IssuePriority {
  id: string;
  name: string;
  icon_name: string | null;
  sort_order: number;
}

export interface IssueLabel {
  id: string;
  name: string;
  color: string;
}

// Label Management System Types
export interface Label {
  id: string;
  name: string;
  color: string;
  created_at: string | null;
}

// Priority Management System Types
export interface Priority {
  id: string;
  name: string;
  icon_name: string | null;
  sort_order: number | null;
  created_at: string | null;
}

// Status Management System Types
export interface Status {
  id: string;
  name: string;
  color: string;
  icon_name: string | null;
  sort_order: number | null;
  created_at: string | null;
}

// Project Management System Types
export interface Project {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  percent_complete: number;
  start_date: string | null;
  target_date: string | null;
  lead_id: string | null;
  status_id: string | null;
  priority_id: string | null;
  health_id: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  is_archived: boolean;

  // New fields for affiliate proposals and member management
  members: string[] | null;
  is_proposed: boolean | null;
  affiliate_user: string | null;

  // New fields for client and budget management
  client_id: string | null;
  budget_id: string | null;

  // Joined data
  lead?: {
    id: string;
    full_name: string | null;
    email: string;
    avatar_url: string | null;
  } | null;
  status?: {
    id: string;
    name: string;
    color: string;
    icon_name: string | null;
  } | null;
  priority?: {
    id: string;
    name: string;
    icon_name: string | null;
    sort_order: number | null;
  } | null;
  health?: {
    id: string;
    name: string;
    color: string;
    description: string | null;
  } | null;
  client?: Client | null;
  budget?: Budget | null;
}

export interface CreateProjectInput {
  name: string;
  description?: string | null;
  icon?: string | null;
  percent_complete?: number;
  start_date?: string | null;
  target_date?: string | null;
  lead_id?: string | null;
  status_id?: string | null;
  priority_id?: string | null;
  health_id?: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
  members?: string[] | null;
  is_proposed?: boolean | null;
  affiliate_user?: string | null;
  client_id?: string | null;
  budget_id?: string | null;
  created_by?: string | null;
}

export interface UpdateProjectInput {
  name?: string;
  description?: string | null;
  icon?: string | null;
  percent_complete?: number;
  start_date?: string | null;
  target_date?: string | null;
  lead_id?: string | null;
  status_id?: string | null;
  priority_id?: string | null;
  health_id?: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
  members?: string[] | null;
  is_proposed?: boolean | null;
  affiliate_user?: string | null;
  client_id?: string | null;
  budget_id?: string | null;
}

export interface IssueProject {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  percent_complete: number;
  start_date: string | null;
  target_date: string | null;
  lead_id: string | null;
  status_id: string | null;
  priority_id: string | null;
  health_id: 'no-update' | 'off-track' | 'on-track' | 'at-risk' | null;
  lead?: IssueUser;
  status?: IssueStatus;
  priority?: IssuePriority;
}

export interface IssueCycle {
  id: string;
  number: number;
  name: string;
  team_id: string;
  start_date: string;
  end_date: string;
  progress: number;
}

export interface Issue {
  id: string;
  identifier: string;
  title: string;
  description: string;
  status_id: string;
  assignee_id: string | null;
  priority_id: string;
  project_id: string | null;
  cycle_id: string | null;
  parent_issue_id: string | null;
  rank: string;
  due_date: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  // Joined data
  status?: IssueStatus;
  assignee?: IssueUser;
  priority?: IssuePriority;
  project?: IssueProject;
  cycle?: IssueCycle;
  labels?: IssueLabel[];
  subissues?: string[];
}

export interface CreateIssueInput {
  title: string;
  description?: string;
  status_id: string;
  assignee_id?: string | null;
  priority_id: string;
  project_id?: string | null;
  cycle_id?: string | null;
  parent_issue_id?: string | null;
  rank: string;
  due_date?: string | null;
  labels?: string[];
}

export interface FilterOptions {
  status?: string[];
  assignee?: string[];
  priority?: string[];
  labels?: string[];
  project?: string[];
}

export interface IssuesState {
  issues: Issue[];
  issuesByStatus: Record<string, Issue[]>;
  loading: boolean;
  error: string | null;
}

// Database table types using the Database schema
export type DatabaseIssue = Database['public']['Tables']['issues']['Row'];
export type DatabaseStatus = Database['public']['Tables']['status']['Row'];
export type DatabasePriority =
  Database['public']['Tables']['priorities']['Row'];
export type DatabaseLabel = Database['public']['Tables']['labels']['Row'];
export type DatabaseProject = Database['public']['Tables']['projects']['Row'];

// Insert and Update types
export type CreateIssueDatabase =
  Database['public']['Tables']['issues']['Insert'];
export type UpdateIssueDatabase =
  Database['public']['Tables']['issues']['Update'];

// ============================================================================
// Teams Management System Types
// ============================================================================

export interface Team {
  id: string;
  name: string;
  icon: string | null;
  color: string | null;
  description: string | null;
  created_at: string | null;
  updated_at: string | null;

  // New fields for member and project management
  members: string[] | null;
  projects: string[] | null;
}

export interface CreateTeamInput {
  id?: string; // Optional - will be generated if not provided
  name: string;
  icon?: string | null;
  color?: string | null;
  description?: string | null;
  members?: string[] | null;
  projects?: string[] | null;
}

export interface TeamMember {
  id: string;
  team_id: string | null;
  user_id: string | null;
  joined: boolean | null;
  joined_at: string | null;
}

export interface CreateTeamMemberInput {
  team_id: string;
  user_id: string;
  joined?: boolean;
}

// ============================================================================
// Email and Query Result Types
// ============================================================================

// Type for project member query results with joined data
export interface ProjectMemberWithRelations {
  user: {
    id: string;
    full_name: string | null;
    email: string;
    avatar_url: string | null;
    role: string;
  } | null;
  project: {
    id: string;
    name: string;
    description: string | null;
    target_date: string | null;
    percent_complete: number | null;
    lead: {
      id: string;
      full_name: string | null;
      email: string;
      avatar_url: string | null;
    } | null;
    status: {
      id: string;
      name: string;
      color: string;
      icon_name: string | null;
    } | null;
    priority: {
      id: string;
      name: string;
      icon_name: string | null;
      sort_order: number | null;
    } | null;
  } | null;
}

// Type for project member query results with profiles join
export interface ProjectMemberQueryResult {
  profiles?: {
    email?: string;
    full_name?: string | null;
    avatar_url?: string | null;
  };
}

// ============================================================================
// Project Members Management System Types
// ============================================================================

export interface ProjectMember {
  id: string;
  project_id: string;
  user_id: string;
  role: 'lead' | 'member' | 'viewer';
  joined_at: string;
  profiles?: {
    id: string;
    full_name: string | null;
    email: string;
    avatar_url: string | null;
    role: string | null;
  };
  projects?: {
    id: string;
    name: string;
  };
}

export interface CreateProjectMemberInput {
  project_id: string;
  user_id: string;
  role?: 'lead' | 'member' | 'viewer';
}

// ============================================================================
// Cycles Management System Types
// ============================================================================

export interface Cycle {
  id: string;
  number: number;
  name: string;
  team_id: string | null;
  start_date: string;
  end_date: string;
  progress: number | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface CreateCycleInput {
  id: string;
  number: number;
  name: string;
  team_id?: string | null;
  start_date: string;
  end_date: string;
  progress?: number;
}

// ============================================================================
// Payments Management System Types
// ============================================================================

export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  payment_method: string | null;
  transaction_id: string | null;
  user_id: string | null;
  project_id: string | null;
  invoice_id: string | null;
  description: string | null;
  metadata: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
}

export interface CreatePaymentInput {
  amount: number;
  currency: string;
  status?: 'pending' | 'completed' | 'failed' | 'cancelled';
  payment_method?: string | null;
  transaction_id?: string | null;
  user_id?: string | null;
  project_id?: string | null;
  invoice_id?: string | null;
  description?: string | null;
  metadata?: Record<string, unknown> | null;
}

// ============================================================================
// Proposals Management System Types
// ============================================================================

export interface Proposal {
  id: number;
  created_at: string;
  user_id: string | null;
  user_email: string | null;
  affiliate_proposal: ReferalProposalType | null;
  is_recieved: boolean | null;
  is_approved: boolean | null;
  completed: boolean | null;
  // Client detail fields
  client_name: string | null;
  client_email: string | null;
  client_phone: string | null;
  client_description: string | null;
  client_id: string | null;
  // Budget field
  proposed_budget: number | null;

  // Joined data
  client?: Client | null;
}

export interface CreateProposalInput {
  user_id?: string | null;
  user_email?: string | null;
  affiliate_proposal?: ReferalProposalType | null;
  is_recieved?: boolean;
  is_approved?: boolean;
  completed?: boolean;
  // Client detail fields
  client_name?: string | null;
  client_email?: string | null;
  client_phone?: string | null;
  client_description?: string | null;
  client_id?: string | null;
  // Budget field
  proposed_budget?: number | null;
}

// ============================================================================
// Applications Management System Types
// ============================================================================

export interface Application {
  id: number;
  created_at: string;
  full_name: string | null;
  email: string;
  phone: string | null;
  position: string | null;
  position_other: string | null;
  message: string | null;
  resume_url: string | null;
  referer: string | null;
  location: string | null;
  user_id: string;
  join_role: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
  reviewed: 'reviewed' | 'received' | 'notAccepted';
  approved: 'accepted' | 'reviewing' | 'notAccepted';
  is_nda_signed: boolean | null;
  nda_signed_date: string | null;
  dob: string | null;
  interests: string | null;
  skills: string | null;
  areas: string[] | null;
  other_area: string | null;
  why_join: string | null;
  hours_per_week: string | null;
  past_experience: string | null;
  available_days: string[] | null;
  preferred_time: string | null;
  equipment: string | null;
  additional_info: string | null;
  newsletter: boolean | null;
  is_vol_form_submited: boolean;
}

export interface CreateApplicationInput {
  full_name?: string | null;
  email: string;
  phone?: string | null;
  position?: string | null;
  position_other?: string | null;
  message?: string | null;
  resume_url?: string | null;
  referer?: string | null;
  location?: string | null;
  user_id: string;
  join_role?: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
  reviewed?: 'reviewed' | 'received' | 'notAccepted';
  approved?: 'accepted' | 'reviewing' | 'notAccepted';
  is_nda_signed?: boolean | null;
  nda_signed_date?: string | null;
  dob?: string | null;
  interests?: string | null;
  skills?: string | null;
  areas?: string[] | null;
  other_area?: string | null;
  why_join?: string | null;
  hours_per_week?: string | null;
  past_experience?: string | null;
  available_days?: string[] | null;
  preferred_time?: string | null;
  equipment?: string | null;
  additional_info?: string | null;
  newsletter?: boolean | null;
  is_vol_form_submited?: boolean;
}

export interface UpdateApplicationInput {
  full_name?: string | null;
  email?: string;
  phone?: string | null;
  position?: string | null;
  position_other?: string | null;
  message?: string | null;
  resume_url?: string | null;
  referer?: string | null;
  location?: string | null;
  join_role?: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
  reviewed?: 'reviewed' | 'received' | 'notAccepted';
  approved?: 'accepted' | 'reviewing' | 'notAccepted';
  is_nda_signed?: boolean | null;
  nda_signed_date?: string | null;
  dob?: string | null;
  interests?: string | null;
  skills?: string | null;
  areas?: string[] | null;
  other_area?: string | null;
  why_join?: string | null;
  hours_per_week?: string | null;
  past_experience?: string | null;
  available_days?: string[] | null;
  preferred_time?: string | null;
  equipment?: string | null;
  additional_info?: string | null;
  newsletter?: boolean | null;
  is_vol_form_submited?: boolean;
}

// ============================================================================
// Invoices Management System Types
// ============================================================================

export interface Invoice {
  id: string;
  invoice_number: string;
  user_id: string | null;
  project_id: string | null;
  amount: number;
  tax_amount: number | null;
  total_amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  issue_date: string;
  due_date: string;
  paid_date: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  is_archived: boolean;
}

export interface CreateInvoiceInput {
  invoice_number: string;
  user_id?: string | null;
  project_id?: string | null;
  amount: number;
  tax_amount?: number | null;
  total_amount: number;
  status?: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date: string;
  notes?: string | null;
}

// ============================================================================
// Members Management System Types
// ============================================================================

export interface Member {
  id: string;
  name: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  status: 'online' | 'offline' | 'away' | null;
  role: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
  username: string | null;
  joined_date: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface CreateMemberInput {
  name: string;
  email: string;
  full_name?: string | null;
  avatar_url?: string | null;
  status?: 'online' | 'offline' | 'away' | null;
  role?: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
  username?: string | null;
}

export interface UpdateMemberInput {
  name?: string;
  email?: string;
  full_name?: string | null;
  avatar_url?: string | null;
  status?: 'online' | 'offline' | 'away' | null;
  role?: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
  username?: string | null;
}

// ============================================================================
// Transactions Management System Types
// ============================================================================

export interface Transaction {
  id: string;
  type: 'income' | 'expense' | 'transfer';
  amount: number;
  currency: string;
  description: string | null;
  category: string | null;
  reference_id: string | null;
  status: 'pending' | 'completed' | 'failed';
  metadata: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
}

export interface CreateTransactionInput {
  type: 'income' | 'expense' | 'transfer';
  amount: number;
  currency: string;
  description?: string | null;
  category?: string | null;
  reference_id?: string | null;
  status?: 'pending' | 'completed' | 'failed';
  metadata?: Record<string, unknown> | null;
}

// ============================================================================
// Affiliate Earnings Management System Types
// ============================================================================

export interface AffiliateEarning {
  id: string;
  user_id: string;
  amount: number;
  commission_rate: number;
  source: string;
  reference_id: string | null;
  earned_date: string;
  status: string;
  paid_date: string | null;
  payment_id: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateAffiliateEarningInput {
  user_id: string;
  amount: number;
  commission_rate: number;
  source: string;
  reference_id?: string | null;
  status?: string;
}

// ============================================================================
// Task Management System Types
// ============================================================================

export interface Task {
  id: string;
  title: string;
  description: string | null;
  project_id: string;
  assigned_to: string | null;
  created_by: string | null;
  status: Database['public']['Enums']['task_status'];
  priority: Database['public']['Enums']['task_priority'];
  due_date: string | null;
  estimated_hours: number | null;
  actual_hours: number | null;
  is_archived: boolean;
  created_at: string;
  updated_at: string;

  // Relationships
  project?: Project | null;
  assigned_user?: Profile_Types | null;
  created_by_user?: Profile_Types | null;
}

export interface CreateTaskInput {
  title: string;
  description?: string | null;
  project_id: string;
  assigned_to?: string | null;
  created_by?: string | null;
  status?: Database['public']['Enums']['task_status'];
  priority?: Database['public']['Enums']['task_priority'];
  due_date?: string | null;
  estimated_hours?: number | null;
  actual_hours?: number | null;
}

// ============================================================================
// Client Management System Types
// ============================================================================

export interface Client {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  description: string | null;
  company_name: string | null;
  industry: string | null;
  contact_person: string | null;
  address: any | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  is_active: boolean;
}

export interface CreateClientInput {
  name: string;
  email?: string | null;
  phone?: string | null;
  description?: string | null;
  company_name?: string | null;
  industry?: string | null;
  contact_person?: string | null;
  address?: any | null;
  created_by?: string | null;
  is_active?: boolean;
}

export interface UpdateClientInput {
  name?: string;
  email?: string | null;
  phone?: string | null;
  description?: string | null;
  company_name?: string | null;
  industry?: string | null;
  contact_person?: string | null;
  address?: any | null;
  is_active?: boolean;
}

// ============================================================================
// Proposal Processing System Types
// ============================================================================

export interface ProposalProcessingResult {
  success: boolean;
  client?: Client;
  project?: Project;
  budget?: Budget;
  errors: string[];
  warnings: string[];
  summary: string;
}

export interface ProposalValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  extractedData: {
    clientData: Partial<CreateClientInput>;
    projectData: Partial<CreateProjectInput>;
    budgetData: Partial<CreateBudgetInput>;
  };
}

// ============================================================================
// WAITLIST TYPES
// ============================================================================

export interface Waitlist {
  id: number;
  created_at: string;
  full_name: string | null;
  email_address: string;
  is_ld_email_sent: boolean | null;
}

export interface CreateWaitlistInput {
  full_name: string;
  email_address: string;
}

export interface UpdateWaitlistInput {
  full_name?: string;
  email_address?: string;
  is_ld_email_sent?: boolean;
}

// ============================================================================
// Budget Management System Types
// ============================================================================

export interface Budget {
  id: string;
  ProjectId: string;
  ClientId: string | null;
  ActualAmount: number;
  CurrentAmount: number;
  Currency: 'USD' | 'EUR' | 'GBP' | 'JPY';
  Category: 'marketing' | 'development' | 'consulting' | 'operations' | 'other';
  Status: 'draft' | 'approved' | 'locked' | 'spent';
  has_affiliate: boolean;
  affiliateId: string | null;
  AffiliateCommission: number | null;
  has_collaborator: boolean;
  collaborators: any | null;
  expense_details: any | null;
  PayoutStatus: 'pending' | 'partially_paid' | 'completed' | null;
  ApprovedBy: string | null;
  ApprovalDate: string | null;
  StartDate: string;
  EndDate: string;
  Notes: string | null;
  created_at: string;
  updated_at: string;

  // Joined data
  project?: Project | null;
  client?: Client | null;
  affiliate?: Profile_Types | null;
  approved_by_user?: Profile_Types | null;
}

export interface CreateBudgetInput {
  ProjectId: string;
  ClientId?: string | null;
  ActualAmount: number;
  CurrentAmount: number;
  Currency?: 'USD' | 'EUR' | 'GBP' | 'JPY';
  Category?:
    | 'marketing'
    | 'development'
    | 'consulting'
    | 'operations'
    | 'other';
  Status?: 'draft' | 'approved' | 'locked' | 'spent';
  has_affiliate?: boolean;
  affiliateId?: string | null;
  AffiliateCommission?: number | null;
  has_collaborator?: boolean;
  collaborators?: any | null;
  expense_details?: any | null;
  PayoutStatus?: 'pending' | 'partially_paid' | 'completed' | null;
  ApprovedBy?: string | null;
  ApprovalDate?: string | null;
  StartDate: string;
  EndDate: string;
  Notes?: string | null;
}

export interface UpdateBudgetInput {
  ClientId?: string | null;
  ActualAmount?: number;
  CurrentAmount?: number;
  Currency?: 'USD' | 'EUR' | 'GBP' | 'JPY';
  Category?:
    | 'marketing'
    | 'development'
    | 'consulting'
    | 'operations'
    | 'other';
  Status?: 'draft' | 'approved' | 'locked' | 'spent';
  has_affiliate?: boolean;
  affiliateId?: string | null;
  AffiliateCommission?: number | null;
  has_collaborator?: boolean;
  collaborators?: any | null;
  expense_details?: any | null;
  PayoutStatus?: 'pending' | 'partially_paid' | 'completed' | null;
  ApprovedBy?: string | null;
  ApprovalDate?: string | null;
  StartDate?: string;
  EndDate?: string;
  Notes?: string | null;
}
